#!/bin/bash

# Azure Deployment Script for Kanban Application
# This script deploys the entire Kanban system to Azure Container Apps

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
RESOURCE_GROUP_NAME="kanban-rg"
LOCATION="westus3"
CONTAINER_REGISTRY_NAME="craftingbyteskanbanregistry"
CONTAINER_APP_ENV_NAME="kanban-env"
DATABASE_CONTAINER_NAME="kanban-neo4j"

# Application names
UI_APP_NAME="kanban-ui"
API_APP_NAME="kanban-api"
AUTH_APP_NAME="kanban-auth"

echo -e "${GREEN}🚀 Starting Azure deployment for Kanban application${NC}"

# Function to check if Azure CLI is installed
check_prerequisites() {
    echo -e "${YELLOW}📋 Checking prerequisites...${NC}"
    
    if ! command -v az &> /dev/null; then
        echo -e "${RED}❌ Azure CLI is not installed. Please install it first.${NC}"
        echo "Install from: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker is not installed. Please install it first.${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Prerequisites check passed${NC}"
}

# Function to login to Azure
azure_login() {
    echo -e "${YELLOW}🔐 Checking Azure login status...${NC}"
    
    if ! az account show &> /dev/null; then
        echo -e "${YELLOW}Please login to Azure...${NC}"
        az login
    fi
    
    echo -e "${GREEN}✅ Azure login confirmed${NC}"
}

# Function to create resource group
create_resource_group() {
    echo -e "${YELLOW}📦 Creating resource group: $RESOURCE_GROUP_NAME in $LOCATION${NC}"
    
    az group create \
        --name $RESOURCE_GROUP_NAME \
        --location $LOCATION \
        --output table
    
    echo -e "${GREEN}✅ Resource group created${NC}"
}

# Function to create container registry
create_container_registry() {
    echo -e "${YELLOW}🏗️ Creating Azure Container Registry: $CONTAINER_REGISTRY_NAME${NC}"
    
    az acr create \
        --resource-group $RESOURCE_GROUP_NAME \
        --name $CONTAINER_REGISTRY_NAME \
        --sku Basic \
        --admin-enabled true \
        --output table
    
    echo -e "${GREEN}✅ Container registry created${NC}"
}

# Function to build and push images
build_and_push_images() {
    echo -e "${YELLOW}🔨 Building and pushing Docker images...${NC}"
    
    # Source environment variables from .env.production
    if [ -f ".env.production" ]; then
        echo -e "${YELLOW}Loading environment variables from .env.production...${NC}"
        export $(grep -v '^#' .env.production | xargs)
    else
        echo -e "${RED}❌ .env.production file not found${NC}"
        exit 1
    fi
    
    # Login to ACR
    az acr login --name $CONTAINER_REGISTRY_NAME
    
    ACR_LOGIN_SERVER=$(az acr show --name $CONTAINER_REGISTRY_NAME --resource-group $RESOURCE_GROUP_NAME --query "loginServer" --output tsv)
    
    # Build and push UI image with build args
    echo -e "${YELLOW}Building UI image...${NC}"
    docker build --platform linux/amd64 -f apps/ui/Dockerfile \
        --build-arg NEXT_PUBLIC_API_URL="$NEXT_PUBLIC_API_URL" \
        --build-arg NEXT_PUBLIC_AUTH_URL="$NEXT_PUBLIC_AUTH_URL" \
        -t $ACR_LOGIN_SERVER/kanban-ui:latest .
    docker push $ACR_LOGIN_SERVER/kanban-ui:latest
    
    # Build and push API image
    echo -e "${YELLOW}Building API image...${NC}"
    docker build --platform linux/amd64 -f apps/api/Dockerfile -t $ACR_LOGIN_SERVER/kanban-api:latest .
    docker push $ACR_LOGIN_SERVER/kanban-api:latest
    
    # Build and push Auth image with build args
    echo -e "${YELLOW}Building Auth image...${NC}"
    docker build --platform linux/amd64 -f apps/auth/Dockerfile \
        --build-arg NEXT_PUBLIC_API_URL="$NEXT_PUBLIC_API_URL" \
        --build-arg NEXT_PUBLIC_UI_URL="$NEXT_PUBLIC_UI_URL" \
        -t $ACR_LOGIN_SERVER/kanban-auth:latest .
    docker push $ACR_LOGIN_SERVER/kanban-auth:latest
    
    echo -e "${GREEN}✅ All images built and pushed${NC}"
}

# Function to create Container Apps Environment
create_container_apps_environment() {
    echo -e "${YELLOW}🌐 Creating Container Apps Environment...${NC}"
    
    # Install Container Apps extension if not already installed
    az extension add --name containerapp --upgrade
    
    # Create the environment
    az containerapp env create \
        --name $CONTAINER_APP_ENV_NAME \
        --resource-group $RESOURCE_GROUP_NAME \
        --location $LOCATION \
        --output table
    
    echo -e "${GREEN}✅ Container Apps Environment created${NC}"
}

# Function to deploy Neo4j database
deploy_neo4j() {
    echo -e "${YELLOW}🗄️ Deploying Neo4j database...${NC}"
    
    az container create \
        --resource-group $RESOURCE_GROUP_NAME \
        --name $DATABASE_CONTAINER_NAME \
        --image neo4j:5.26 \
        --os-type Linux \
        --dns-name-label kanban-neo4j-$(date +%s) \
        --ports 7474 7687 \
        --environment-variables \
            NEO4J_AUTH="neo4j/$NEO4J_PASSWORD" \
            NEO4J_PLUGINS='["apoc"]' \
            NEO4J_dbms_security_procedures_unrestricted=apoc.* \
        --cpu 1 \
        --memory 2 \
        --location $LOCATION \
        --output table
    
    echo -e "${GREEN}✅ Neo4j database deployed${NC}"
}

# Function to deploy container apps
deploy_container_apps() {
    echo -e "${YELLOW}📱 Deploying container applications...${NC}"
    
    ACR_LOGIN_SERVER=$(az acr show --name $CONTAINER_REGISTRY_NAME --resource-group $RESOURCE_GROUP_NAME --query "loginServer" --output tsv)
    ACR_USERNAME=$(az acr credential show --name $CONTAINER_REGISTRY_NAME --resource-group $RESOURCE_GROUP_NAME --query "username" --output tsv)
    ACR_PASSWORD=$(az acr credential show --name $CONTAINER_REGISTRY_NAME --resource-group $RESOURCE_GROUP_NAME --query "passwords[0].value" --output tsv)
    
    # Get Neo4j connection details
    NEO4J_FQDN=$(az container show --resource-group $RESOURCE_GROUP_NAME --name $DATABASE_CONTAINER_NAME --query "ipAddress.fqdn" --output tsv)
    
    # Deploy API first (since other apps depend on it)
    echo -e "${YELLOW}Deploying API application...${NC}"
    az containerapp create \
        --name $API_APP_NAME \
        --resource-group $RESOURCE_GROUP_NAME \
        --environment $CONTAINER_APP_ENV_NAME \
        --image $ACR_LOGIN_SERVER/kanban-api:latest \
        --registry-server $ACR_LOGIN_SERVER \
        --registry-username $ACR_USERNAME \
        --registry-password $ACR_PASSWORD \
        --target-port 4000 \
        --ingress external \
        --env-vars \
            NODE_ENV=production \
            PORT=4000 \
            NEO4J_URI=bolt://$NEO4J_FQDN:7687 \
            NEO4J_USER=neo4j \
            NEO4J_PASSWORD="$NEO4J_PASSWORD" \
            JWT_SECRET="$JWT_SECRET" \
            CORS_ORIGIN=placeholder-will-update \
        --cpu 0.5 \
        --memory 1Gi \
        --min-replicas 1 \
        --max-replicas 3
    
    # Get API URL
    API_URL=$(az containerapp show --name $API_APP_NAME --resource-group $RESOURCE_GROUP_NAME --query "properties.configuration.ingress.fqdn" --output tsv)
    
    # Deploy Auth application with placeholder URLs
    echo -e "${YELLOW}Deploying Auth application...${NC}"
    az containerapp create \
        --name $AUTH_APP_NAME \
        --resource-group $RESOURCE_GROUP_NAME \
        --environment $CONTAINER_APP_ENV_NAME \
        --image $ACR_LOGIN_SERVER/kanban-auth:latest \
        --registry-server $ACR_LOGIN_SERVER \
        --registry-username $ACR_USERNAME \
        --registry-password $ACR_PASSWORD \
        --target-port 3000 \
        --ingress external \
        --env-vars \
            NODE_ENV=production \
            NEXT_PUBLIC_API_URL=https://$API_URL \
            NEXT_PUBLIC_UI_URL=placeholder-will-update \
            JWT_SECRET="$JWT_SECRET" \
            SESSION_SECRET="$SESSION_SECRET" \
            GOOGLE_CLIENT_ID="$GOOGLE_CLIENT_ID" \
            GOOGLE_CLIENT_SECRET="$GOOGLE_CLIENT_SECRET" \
            NEXTAUTH_URL=placeholder-will-update \
            NEXTAUTH_SECRET="$NEXTAUTH_SECRET" \
        --cpu 0.5 \
        --memory 1Gi \
        --min-replicas 1 \
        --max-replicas 3
    
    # Deploy UI application with placeholder URLs
    echo -e "${YELLOW}Deploying UI application...${NC}"
    az containerapp create \
        --name $UI_APP_NAME \
        --resource-group $RESOURCE_GROUP_NAME \
        --environment $CONTAINER_APP_ENV_NAME \
        --image $ACR_LOGIN_SERVER/kanban-ui:latest \
        --registry-server $ACR_LOGIN_SERVER \
        --registry-username $ACR_USERNAME \
        --registry-password $ACR_PASSWORD \
        --target-port 3000 \
        --ingress external \
        --env-vars \
            NODE_ENV=production \
            NEXT_PUBLIC_API_URL=https://$API_URL \
            NEXT_PUBLIC_AUTH_URL=placeholder-will-update \
        --cpu 0.5 \
        --memory 1Gi \
        --min-replicas 1 \
        --max-replicas 3
    
    # Get actual URLs from both deployed services
    echo -e "${YELLOW}Getting actual URLs from deployed services...${NC}"
    AUTH_URL=$(az containerapp show --name $AUTH_APP_NAME --resource-group $RESOURCE_GROUP_NAME --query "properties.configuration.ingress.fqdn" --output tsv)
    UI_URL=$(az containerapp show --name $UI_APP_NAME --resource-group $RESOURCE_GROUP_NAME --query "properties.configuration.ingress.fqdn" --output tsv)
    
    # Update Auth service with correct URLs
    echo -e "${YELLOW}Updating Auth service with correct URLs...${NC}"
    az containerapp update \
        --name $AUTH_APP_NAME \
        --resource-group $RESOURCE_GROUP_NAME \
        --set-env-vars \
            NEXT_PUBLIC_UI_URL=https://$UI_URL \
            NEXTAUTH_URL=https://$AUTH_URL
    
    # Update UI service with correct Auth URL
    echo -e "${YELLOW}Updating UI service with correct Auth URL...${NC}"
    az containerapp update \
        --name $UI_APP_NAME \
        --resource-group $RESOURCE_GROUP_NAME \
        --set-env-vars NEXT_PUBLIC_AUTH_URL=https://$AUTH_URL
    
    # Update API service with correct CORS origins
    echo -e "${YELLOW}Updating API service with correct CORS origins...${NC}"
    az containerapp update \
        --name $API_APP_NAME \
        --resource-group $RESOURCE_GROUP_NAME \
        --set-env-vars CORS_ORIGIN=https://$UI_URL,https://$AUTH_URL
    
    echo -e "${GREEN}✅ All container applications deployed${NC}"
}

# Function to update .env.production with actual URLs
update_env_production() {
    echo -e "${YELLOW}📝 Updating .env.production with actual deployment URLs...${NC}"
    
    UI_URL=$(az containerapp show --name $UI_APP_NAME --resource-group $RESOURCE_GROUP_NAME --query "properties.configuration.ingress.fqdn" --output tsv)
    API_URL=$(az containerapp show --name $API_APP_NAME --resource-group $RESOURCE_GROUP_NAME --query "properties.configuration.ingress.fqdn" --output tsv)
    AUTH_URL=$(az containerapp show --name $AUTH_APP_NAME --resource-group $RESOURCE_GROUP_NAME --query "properties.configuration.ingress.fqdn" --output tsv)
    
    # Update .env.production file with actual URLs
    sed -i.backup "s|NEXT_PUBLIC_API_URL=.*|NEXT_PUBLIC_API_URL=https://$API_URL|g" .env.production
    sed -i.backup "s|NEXT_PUBLIC_AUTH_URL=.*|NEXT_PUBLIC_AUTH_URL=https://$AUTH_URL|g" .env.production
    sed -i.backup "s|NEXT_PUBLIC_UI_URL=.*|NEXT_PUBLIC_UI_URL=https://$UI_URL|g" .env.production
    sed -i.backup "s|API_URL=.*|API_URL=https://$API_URL|g" .env.production
    sed -i.backup "s|AUTH_URL=.*|AUTH_URL=https://$AUTH_URL|g" .env.production
    sed -i.backup "s|UI_URL=.*|UI_URL=https://$UI_URL|g" .env.production
    sed -i.backup "s|NEXTAUTH_URL=.*|NEXTAUTH_URL=https://$AUTH_URL|g" .env.production
    sed -i.backup "s|CORS_ORIGIN=.*|CORS_ORIGIN=https://$UI_URL,https://$AUTH_URL|g" .env.production
    
    echo -e "${GREEN}✅ .env.production updated with actual deployment URLs${NC}"
}

# Function to display deployment info
show_deployment_info() {
    echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
    echo ""
    echo -e "${YELLOW}📋 Deployment Information:${NC}"
    echo "=================================="
    
    UI_URL=$(az containerapp show --name $UI_APP_NAME --resource-group $RESOURCE_GROUP_NAME --query "properties.configuration.ingress.fqdn" --output tsv)
    API_URL=$(az containerapp show --name $API_APP_NAME --resource-group $RESOURCE_GROUP_NAME --query "properties.configuration.ingress.fqdn" --output tsv)
    AUTH_URL=$(az containerapp show --name $AUTH_APP_NAME --resource-group $RESOURCE_GROUP_NAME --query "properties.configuration.ingress.fqdn" --output tsv)
    NEO4J_FQDN=$(az container show --resource-group $RESOURCE_GROUP_NAME --name $DATABASE_CONTAINER_NAME --query "ipAddress.fqdn" --output tsv)
    
    echo "🌐 UI Application: https://$UI_URL"
    echo "🔗 API Endpoint: https://$API_URL"
    echo "🔐 Auth Service: https://$AUTH_URL"
    echo "🗄️ Neo4j Database: $NEO4J_FQDN:7474"
    echo ""
    echo -e "${YELLOW}⚠️ Next Steps:${NC}"
    echo "1. Update Google OAuth redirect URIs to include: https://$AUTH_URL/api/auth/callback/google"
    echo "2. Fill in your production secrets in .env.production if not already done"
    echo "3. Set up custom domain names (optional)"
    echo "4. Configure SSL certificates (automatic with Container Apps)"
    echo ""
    echo -e "${GREEN}✅ Your .env.production file has been automatically updated with the correct URLs!${NC}"
    echo ""
    echo -e "${RED}🔒 Security Note:${NC}"
    echo "Remember to update the placeholder secrets in the environment variables with your actual production values!"
}

# Main deployment function
main() {
    check_prerequisites
    azure_login
    create_resource_group
    create_container_registry
    build_and_push_images
    create_container_apps_environment
    deploy_neo4j
    deploy_container_apps
    update_env_production
    show_deployment_info
}

# Run the main function
main "$@"