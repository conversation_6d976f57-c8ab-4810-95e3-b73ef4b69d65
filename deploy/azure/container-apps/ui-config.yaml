apiVersion: 2022-10-01
location: westus3
resourceGroup: kanban-rg
name: kanban-ui
properties:
  configuration:
    activeRevisionsMode: Single
    ingress:
      external: true
      targetPort: 3000
      allowInsecure: false
    registries:
    - server: craftingbyteskanbanregistry.azurecr.io
      username: craftingbyteskanbanregistry
      passwordSecretRef: registry-password
    secrets:
    - name: registry-password
      value: "" # Will be populated during deployment
  template:
    containers:
    - image: craftingbyteskanbanregistry.azurecr.io/kanban-ui:latest
      name: kanban-ui
      resources:
        cpu: 0.5
        memory: 1Gi
      env:
      - name: NODE_ENV
        value: "production"
      - name: NEXT_PUBLIC_API_URL
        value: "" # Will be set during deployment
      - name: NEXT_PUBLIC_AUTH_URL
        value: "" # Will be set during deployment
    scale:
      minReplicas: 1
      maxReplicas: 3
      rules:
      - name: http-scale
        http:
          metadata:
            concurrentRequests: "100"