apiVersion: 2022-10-01
location: westus3
resourceGroup: kanban-rg
name: kanban-auth
properties:
  configuration:
    activeRevisionsMode: Single
    ingress:
      external: true
      targetPort: 3000
      allowInsecure: false
    registries:
    - server: craftingbyteskanbanregistry.azurecr.io
      username: craftingbyteskanbanregistry
      passwordSecretRef: registry-password
    secrets:
    - name: registry-password
      value: "" # Will be populated during deployment
    - name: jwt-secret
      value: "" # Will be populated during deployment
    - name: session-secret
      value: "" # Will be populated during deployment
    - name: google-client-secret
      value: "" # Will be populated during deployment
    - name: nextauth-secret
      value: "" # Will be populated during deployment
  template:
    containers:
    - image: craftingbyteskanbanregistry.azurecr.io/kanban-auth:latest
      name: kanban-auth
      resources:
        cpu: 0.5
        memory: 1Gi
      env:
      - name: NODE_ENV
        value: "production"
      - name: NEXT_PUBLIC_API_URL
        value: "" # Will be set during deployment
      - name: NEXT_PUBLIC_UI_URL
        value: "" # Will be set during deployment
      - name: JWT_SECRET
        secretRef: jwt-secret
      - name: SESSION_SECRET
        secretRef: session-secret
      - name: GOOGLE_CLIENT_ID
        value: "" # Will be set from .env.production
      - name: GOOGLE_CLIENT_SECRET
        secretRef: google-client-secret
      - name: NEXTAUTH_URL
        value: "" # Will be set during deployment
      - name: NEXTAUTH_SECRET
        secretRef: nextauth-secret
    scale:
      minReplicas: 1
      maxReplicas: 3
      rules:
      - name: http-scale
        http:
          metadata:
            concurrentRequests: "100"