apiVersion: 2022-10-01
location: westus3
resourceGroup: kanban-rg
name: kanban-api
properties:
  configuration:
    activeRevisionsMode: Single
    ingress:
      external: true
      targetPort: 4000
      allowInsecure: false
    registries:
    - server: craftingbyteskanbanregistry.azurecr.io
      username: craftingbyteskanbanregistry
      passwordSecretRef: registry-password
    secrets:
    - name: registry-password
      value: "" # Will be populated during deployment
    - name: neo4j-password
      value: "" # Will be populated during deployment
    - name: jwt-secret
      value: "" # Will be populated during deployment
  template:
    containers:
    - image: craftingbyteskanbanregistry.azurecr.io/kanban-api:latest
      name: kanban-api
      resources:
        cpu: 0.5
        memory: 1Gi
      env:
      - name: NODE_ENV
        value: "production"
      - name: PORT
        value: "4000"
      - name: NEO4J_URI
        value: "" # Will be set during deployment
      - name: NEO4J_USER
        value: "neo4j"
      - name: NEO4J_PASSWORD
        secretRef: neo4j-password
      - name: JWT_SECRET
        secretRef: jwt-secret
      - name: CORS_ORIGIN
        value: "" # Will be set during deployment
    scale:
      minReplicas: 1
      maxReplicas: 3
      rules:
      - name: http-scale
        http:
          metadata:
            concurrentRequests: "100"