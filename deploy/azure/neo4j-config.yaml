apiVersion: 2021-03-01
location: westus3
resourceGroup: kanban-rg
name: kanban-neo4j
properties:
  containers:
  - name: neo4j
    properties:
      image: neo4j:5.26
      resources:
        requests:
          cpu: 1
          memoryInGB: 2
      ports:
      - port: 7474
        protocol: TCP
      - port: 7687
        protocol: TCP
      environmentVariables:
      - name: NEO4J_AUTH
        value: neo4j/your-secure-production-password
      - name: NEO4J_PLUGINS
        value: '["apoc"]'
      - name: NEO4J_dbms_security_procedures_unrestricted
        value: apoc.*
      - name: NEO4J_apoc_export_file_enabled
        value: "true"
      - name: NEO4J_apoc_import_file_enabled
        value: "true"
      volumeMounts:
      - name: neo4j-data
        mountPath: /data
      - name: neo4j-logs
        mountPath: /logs
  volumes:
  - name: neo4j-data
    azureFile:
      shareName: neo4j-data
      storageAccountName: "" # Will be created during deployment
      storageAccountKey: "" # Will be set during deployment
  - name: neo4j-logs
    azureFile:
      shareName: neo4j-logs
      storageAccountName: "" # Will be created during deployment
      storageAccountKey: "" # Will be set during deployment
  osType: Linux
  ipAddress:
    type: Public
    ports:
    - protocol: TCP
      port: 7474
    - protocol: TCP
      port: 7687
    dnsNameLabel: kanban-neo4j
  restartPolicy: Always