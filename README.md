# Kanban Mono-Repo

A TurboRepo-based mono-repo for a Kanban application with TypeScript, React, and GraphQL.

## Architecture

- **UI App** (`apps/ui`): Next.js React application on port 3001
- **API App** (`apps/api`): GraphQL API server on port 4000
- **Auth App** (`apps/auth`): OIDC Authentication app on port 3003
- **Shared Packages**:
  - `@kanban/tsconfig`: Shared TypeScript configurations
  - `@kanban/graphql-schema`: Shared GraphQL schema and types

## Quick Start

1. Install dependencies:
   ```bash
   pnpm install
   ```

2. Build all packages:
   ```bash
   pnpm build
   ```

3. Run all apps in development mode:
   ```bash
   pnpm dev
   ```

## Individual App Development

- **UI only**: `pnpm dev:ui`
- **API only**: `pnpm dev:api`
- **Auth only**: `pnpm dev:auth`

## App URLs

- UI: http://localhost:3001
- API: http://localhost:4000 (GraphQL: http://localhost:4000/graphql)
- Auth: http://localhost:3003

## Authentication Flow

1. User visits UI app
2. Clicks "Login" button
3. Redirects to Auth app
4. After login, redirects back to UI app
5. UI app shows kanban boards from API

## Project Structure

```
kanban/
├── apps/
│   ├── ui/          # Next.js React app
│   ├── api/         # GraphQL API server
│   └── auth/        # Authentication app
├── packages/
│   ├── tsconfig/    # Shared TypeScript configs
│   └── graphql-schema/ # Shared GraphQL schema
├── package.json     # Root package.json
├── turbo.json       # TurboRepo configuration
└── pnpm-workspace.yaml
```