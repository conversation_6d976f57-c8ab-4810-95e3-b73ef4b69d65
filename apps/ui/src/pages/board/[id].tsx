import React, { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/router";
import {
  Board,
  useGetBoardWithColumnsQuery,
  useMoveCardMutation,
  useReorderCardsInColumnMutation,
} from "@kanban/graphql-schema/dist/client";
import { decodeUrlSafeBase64ToGuid } from "@kanban/graphql-schema/dist/guidutils";
import {
  ThemeProvider,
  Container,
  Typography,
  createThemeWithMode,
  type PaletteMode,
} from "@kanban/design-system";
import {
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { NavigationBar, BoardHeader, BoardContent } from "../../components";
import { useAuth } from "../../contexts/AuthContext";

export default function BoardPage() {
  const router = useRouter();
  const { id } = router.query;
  const { authToken, isLoading: authLoading, logout } = useAuth();
  
  // Decode the URL-safe base64 ID to a full GUID
  const boardId = useMemo(() => {
    if (!id || typeof id !== 'string') return undefined;
    return decodeUrlSafeBase64ToGuid(id) || undefined;
  }, [id]);
  const [themeMode, setThemeMode] = useState<PaletteMode>("light");
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [overId, setOverId] = useState<string | null>(null);
  const [collapsedColumns, setCollapsedColumns] = useState<Set<string>>(
    new Set()
  );

  const [localBoardData, setLocalBoardData] = useState<Board | null>(null);

  // Create dynamic theme based on current mode
  const currentTheme = createThemeWithMode(themeMode);

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px movement required to start drag
      },
    })
  );

  // GraphQL query to fetch board data
  const { data, loading, error, refetch } = useGetBoardWithColumnsQuery({
    variables: { boardId: boardId as string },
    skip: !router.isReady || !boardId || authLoading || !authToken,
  });

  // GraphQL mutations for card movement
  const [moveCardMutation] = useMoveCardMutation();
  const [reorderCardsInColumnMutation] = useReorderCardsInColumnMutation();

  const board = data?.boards?.[0] as any as Board;

  // Sort columns according to board.columnOrder if needed
  const sortedColumns = React.useMemo(() => {
    if (!board?.columns) return [];

    let columns = [...board.columns];
    if (board.columnOrder && board.columnOrder.length > 0) {
      const columnMap = new Map(columns.map((col) => [col.externalId, col]));
      const orderedColumns = board.columnOrder
        .map((id) => columnMap.get(id))
        .filter((col): col is NonNullable<typeof col> => col !== undefined);
      const unorderedColumns = columns.filter(
        (col) => !board.columnOrder.includes(col.externalId)
      );
      columns = [...orderedColumns, ...unorderedColumns];
    }
    return columns;
  }, [board]);

  // Sync GraphQL board data to local state for drag & drop
  React.useEffect(() => {
    if (board && !localBoardData) {
      const boardWithSortedColumns = {
        ...board,
        columns: sortedColumns,
      } as Board;
      setLocalBoardData(boardWithSortedColumns);
    }
  }, [board, localBoardData, sortedColumns]);

  // // Use local board data for drag & drop, fallback to GraphQL board
  // const boardData = localBoardData || board;

  useEffect(() => {
    // Don't redirect if auth is still loading
    if (authLoading) return;

    if (!authToken) {
      router.push("/");
      return;
    }
  }, [authToken, authLoading, router]);

  useEffect(() => {
    // Load saved theme preference
    const savedTheme = localStorage.getItem("themeMode") as PaletteMode;
    if (savedTheme && (savedTheme === "light" || savedTheme === "dark")) {
      setThemeMode(savedTheme);
    }
  }, []);

  // Separate useEffect for window resize handling
  useEffect(() => {
    const handleResize = () => {
      // Force re-render on window resize for responsive columns
      setCollapsedColumns((prev) => new Set(prev));
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleThemeToggle = () => {
    const newMode = themeMode === "light" ? "dark" : "light";
    setThemeMode(newMode);
    localStorage.setItem("themeMode", newMode);
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
  };

  // Column toggle handler
  const handleColumnToggle = (columnId: string) => {
    setCollapsedColumns((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(columnId)) {
        newSet.delete(columnId);
      } else {
        newSet.add(columnId);
      }
      return newSet;
    });
  };

  // Update card handler
  const handleUpdateCard = (
    cardId: string,
    title: string,
    description: string
  ) => {
    setLocalBoardData((prev) => {
      if (!prev) return prev;

      return {
        ...prev,
        columns: prev.columns.map((col) => ({
          ...col,
          cards:
            col.cards?.map((card) =>
              card.externalId === cardId
                ? { ...card, title, description: description || "" }
                : card
            ) || [],
        })),
      };
    });
  };

  // Delete card handler
  const handleDeleteCard = (cardId: string) => {
    setLocalBoardData((prev) => {
      if (!prev) return prev;

      return {
        ...prev,
        columns: prev.columns.map((col) => ({
          ...col,
          cards: col.cards?.filter((card) => card.externalId !== cardId) || [],
        })),
      };
    });
  };

  // Card creation handler - refresh board data after API call
  const handleCardCreated = async () => {
    // Force a refetch of the board data
    setLocalBoardData(null);
    if (refetch) {
      await refetch();
    }
  };

  // Drag and drop handlers
  const handleDragStart = (event: DragStartEvent) => {
    try {
      setActiveId(event.active.id as string);
      setOverId(null);
    } catch (error) {
      console.error("Error in handleDragStart:", error);
      setActiveId(null);
      setOverId(null);
    }
  };

  const handleDragOver = (event: DragOverEvent) => {
    try {
      const { over } = event;
      setOverId(over ? (over.id as string) : null);
    } catch (error) {
      console.error("Error in handleDragOver:", error);
      setOverId(null);
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    try {
      const { active, over } = event;
      setActiveId(null);
      setOverId(null);

      if (!over || !localBoardData || !localBoardData.columns) return;

      const activeId = active.id as string;
      const overId = over.id as string;

      const activeContainer = findContainer(activeId);
      const overContainer = findContainer(overId);

      if (!activeContainer || !overContainer) return;

      if (activeContainer === overContainer) {
        // Reorder within same column
        let newCards: any[] = [];
        let shouldUpdate = false;

        setLocalBoardData((prev) => {
          if (!prev) return prev;

          const column = prev.columns.find(
            (col) => col.externalId === activeContainer
          );
          if (!column) return prev;

          const cards = [...column.cards];
          const activeIndex = cards.findIndex(
            (card) => card.externalId === activeId
          );

          let targetIndex;
          if (overId.startsWith("top-")) {
            // Dropping on top drop zone - insert at beginning
            targetIndex = 0;
          } else {
            // Dropping on another card - insert after that card
            const overIndex = cards.findIndex(
              (card) => card.externalId === overId
            );
            targetIndex = overIndex >= 0 ? overIndex + 1 : cards.length;
          }

          if (
            activeIndex !== -1 &&
            targetIndex !== activeIndex &&
            targetIndex !== activeIndex + 1
          ) {
            const [reorderedCard] = cards.splice(activeIndex, 1);
            // Adjust target index if we removed an item before it
            const adjustedTargetIndex =
              targetIndex > activeIndex ? targetIndex - 1 : targetIndex;
            cards.splice(adjustedTargetIndex, 0, reorderedCard);

            newCards = cards;
            shouldUpdate = true;

            return {
              ...prev,
              columns: prev.columns.map((col) =>
                col.externalId === activeContainer ? { ...col, cards } : col
              ),
            };
          }
          return prev;
        });

        // Persist same-column reordering to API
        if (shouldUpdate && newCards.length > 0) {
          try {
            await reorderCardsInColumnMutation({
              variables: {
                columnId: activeContainer,
                cardPriorityOrder: newCards.map((card) => card.externalId),
              },
            });
          } catch (error) {
            console.error("Error persisting card reorder:", error);
            // Optionally revert local state or show error message
          }
        }
      } else {
        // Move between different columns
        let newColumnCards: any[] = [];
        let oldColumnCards: any[] = [];
        let shouldUpdate = false;

        setLocalBoardData((prev) => {
          if (!prev) return prev;

          // Find the card being moved
          const activeColumn = prev.columns.find(
            (col) => col.externalId === activeContainer
          );
          const overColumn = prev.columns.find(
            (col) => col.externalId === overContainer
          );

          if (!activeColumn || !overColumn) return prev;

          const cardToMove = activeColumn.cards.find(
            (card) => card.externalId === activeId
          );
          if (!cardToMove) return prev;

          // Find insertion index in target column
          let insertIndex = overColumn.cards.length; // Default to end of column

          // Handle different drop targets
          if (overId.startsWith("top-")) {
            // Dropping on top drop zone - insert at beginning
            insertIndex = 0;
          } else if (overId === overContainer) {
            // Dropping on column container - insert at end
            insertIndex = overColumn.cards.length;
          } else {
            // Dropping on another card - insert after that card
            const overIndex = overColumn.cards.findIndex(
              (card) => card.externalId === overId
            );
            insertIndex =
              overIndex >= 0 ? overIndex + 1 : overColumn.cards.length;
          }

          // Calculate new card orders
          oldColumnCards = activeColumn.cards.filter(
            (card) => card.externalId !== activeId
          );
          const newTargetCards = [...overColumn.cards];
          newTargetCards.splice(insertIndex, 0, cardToMove);
          newColumnCards = newTargetCards;
          shouldUpdate = true;

          return {
            ...prev,
            columns: prev.columns.map((col) => {
              if (col.externalId === activeContainer) {
                // Remove card from source column
                return {
                  ...col,
                  cards: oldColumnCards,
                };
              } else if (col.externalId === overContainer) {
                // Add card to target column at the right position
                return {
                  ...col,
                  cards: newColumnCards,
                };
              }
              return col;
            }),
          };
        });

        // Persist cross-column move to API
        if (shouldUpdate) {
          try {
            await moveCardMutation({
              variables: {
                cardId: activeId,
                newColumnId: overContainer,
                oldColumnId: activeContainer,
                newColumnCardOrder: newColumnCards.map((card) => card.externalId),
                oldColumnCardOrder: oldColumnCards.map((card) => card.externalId),
              },
            });
          } catch (error) {
            console.error("Error persisting card move:", error);
            // Optionally revert local state or show error message
          }
        }
      }
    } catch (error) {
      console.error("Error in handleDragEnd:", error);
      setActiveId(null);
    }
  };

  const findContainer = (id: string): string | null => {
    try {
      if (!localBoardData?.columns) {
        return null;
      }

      // Handle top drop zones (top-columnId)
      if (id.startsWith("top-")) {
        const columnId = id.replace("top-", "");
        return (
          localBoardData.columns.find((col) => col.externalId === columnId)
            ?.externalId || null
        );
      }

      // Check if it's a card ID
      for (const column of localBoardData.columns) {
        const card = column.cards.find((card) => card.externalId === id);
        if (card) {
          return column.externalId;
        }
      }

      // Check if it's a column ID
      return (
        localBoardData.columns.find((col) => col.externalId === id)
          ?.externalId || null
      );
    } catch (error) {
      console.error("Error finding container:", error);
      return null;
    }
  };

  // Calculate responsive column dimensions
  const columnDimensions = React.useMemo(() => {
    if (!sortedColumns?.length) {
      return { width: 300, needsScroll: false };
    }

    const minWidth = 220;
    const idealWidth = 300;
    const maxWidth = 350;
    const collapsedWidth = 60;
    const gap = 8;

    const availableWidth =
      typeof window !== "undefined" ? window.innerWidth - 48 : 1200;
    const expandedColumns = sortedColumns.length - collapsedColumns.size;
    const collapsedColumnsWidth =
      collapsedColumns.size * (collapsedWidth + gap);
    const gapsWidth = (sortedColumns.length - 1) * gap;
    const availableForExpanded =
      availableWidth - collapsedColumnsWidth - gapsWidth;

    let width = idealWidth;
    if (expandedColumns > 0) {
      const idealTotalWidth = expandedColumns * idealWidth;
      if (idealTotalWidth > availableForExpanded) {
        width = Math.max(
          minWidth,
          Math.floor(availableForExpanded / expandedColumns)
        );
      } else if (idealTotalWidth < availableForExpanded) {
        width = Math.min(
          maxWidth,
          Math.floor(availableForExpanded / expandedColumns)
        );
      }
    }

    const totalWidth =
      expandedColumns * width + collapsedColumnsWidth + gapsWidth;
    const needsScroll = totalWidth > availableWidth;

    return { width, needsScroll };
  }, [sortedColumns, collapsedColumns]);

  const showMessage = useMemo(
    () =>
      authLoading ||
      !authToken ||
      loading ||
      error ||
      (!loading && !localBoardData),
    [authLoading, authToken, localBoardData, loading, error]
  );

  const message = useMemo(() => {
    if (authLoading) return "Checking authentication...";
    if (!authToken) return "Checking authentication...";
    if (loading) return "Loading board...";
    if (error) return `Error loading board: ${error.message}`;
    if (!localBoardData) return "Board not found";
    return null;
  }, [authLoading, authToken, loading, error, localBoardData]);

  if (showMessage) {
    return (
      <ThemeProvider theme={currentTheme}>
        <Container maxWidth="md" sx={{ minHeight: "100vh", py: 4 }}>
          <Typography>{message}</Typography>
        </Container>
      </ThemeProvider>
    );
  }

  return (
    !!localBoardData && (
      <ThemeProvider theme={currentTheme}>
        <Container maxWidth={false} disableGutters sx={{ minHeight: "100vh" }}>
          {/* Navigation Bar */}
          <NavigationBar
            themeMode={themeMode}
            onThemeToggle={handleThemeToggle}
            anchorEl={anchorEl}
            onMenuOpen={handleClick}
            onMenuClose={handleClose}
            onLogout={handleLogout}
          />

          {/* Board Header */}
          <BoardHeader boardName={(localBoardData as any)?.name || ""} />

          {/* Board Content */}
          <BoardContent
            boardData={localBoardData}
            sensors={sensors}
            activeId={activeId}
            overId={overId}
            collapsedColumns={collapsedColumns}
            columnDimensions={columnDimensions}
            onDragStart={handleDragStart}
            onDragOver={handleDragOver}
            onDragEnd={handleDragEnd}
            onColumnToggle={handleColumnToggle}
            onCardCreated={handleCardCreated}
            onUpdateCard={handleUpdateCard}
            onDeleteCard={handleDeleteCard}
          />
        </Container>
      </ThemeProvider>
    )
  );
}
