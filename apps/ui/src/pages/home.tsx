import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import {
  useGetMeAndBoardsQuery,
  GetMeAndBoardsQuery,
} from "@kanban/graphql-schema/dist/client";
import { encodeGuidToUrlSafeBase64 } from "@kanban/graphql-schema/dist/guidutils";
import {
  ThemeProvider,
  Container,
  Card,
  Button,
  Typography,
  Box,
  Stack,
  CircularProgress,
  Alert,
} from "@kanban/design-system";
import { CreateBoardDialog } from "../components";

export default function Home() {
  const router = useRouter();
  const [authToken, setAuthToken] = useState<string | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  const { data, loading, error, refetch } = useGetMeAndBoardsQuery({
    skip: !authToken,
    context: {
      headers: {
        authorization: authToken ? `Bearer ${authToken}` : "",
      },
    },
  });

  useEffect(() => {
    // Wait for router to be ready before processing
    if (!router.isReady) return;

    // Get token from URL params or localStorage
    const tokenFromUrl = router.query.token as string;
    const tokenFromStorage = localStorage.getItem("authToken");

    const token = tokenFromUrl || tokenFromStorage;

    if (token) {
      setAuthToken(token);
      localStorage.setItem("authToken", token);

      // Clean up URL if token was from URL
      if (tokenFromUrl) {
        router.replace("/home", undefined, { shallow: true });
      }
    } else {
      // No token, redirect to auth
      window.location.href =
        process.env.NEXT_PUBLIC_AUTH_URL || (() => { throw new Error('NEXT_PUBLIC_AUTH_URL environment variable is required') })();
    }
  }, [router, router.isReady, router.query]);

  const handleLogout = () => {
    localStorage.removeItem("authToken");
    setAuthToken(null);
    window.location.href =
      process.env.NEXT_PUBLIC_AUTH_URL || (() => { throw new Error('NEXT_PUBLIC_AUTH_URL environment variable is required') })();
  };

  const handleOpenCreateDialog = () => {
    setIsCreateDialogOpen(true);
  };

  const handleCloseCreateDialog = () => {
    setIsCreateDialogOpen(false);
  };

  const handleBoardCreated = async () => {
    await refetch();
  };

  if (!authToken) {
    return (
      <ThemeProvider>
        <Container centered>
          <Typography>Checking authentication...</Typography>
        </Container>
      </ThemeProvider>
    );
  }

  if (loading) {
    return (
      <ThemeProvider>
        <Container centered>
          <Stack spacing={2} alignItems="center">
            <CircularProgress />
            <Typography>Loading...</Typography>
          </Stack>
        </Container>
      </ThemeProvider>
    );
  }

  if (error) {
    return (
      <ThemeProvider>
        <Container centered>
          <Stack spacing={3} alignItems="center">
            <Alert severity="error">Error: {error.message}</Alert>
            <Button variant="primary" onClick={() => refetch()}>
              Retry
            </Button>
          </Stack>
        </Container>
      </ThemeProvider>
    );
  }

  const user = data?.me;
  const boards = data?.boards || [];

  return (
    <ThemeProvider>
      <Container>
        <Stack spacing={4} sx={{ py: 4 }}>
          {/* Header */}
          <Card>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Box>
                <Typography variant="h1">Kanban Dashboard</Typography>
                {user && (
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mt: 1 }}
                  >
                    Welcome back, {user.name}! ({user.email})
                  </Typography>
                )}
              </Box>
              <Button variant="danger" onClick={handleLogout}>
                Logout
              </Button>
            </Box>
          </Card>

          {/* Boards Section */}
          <Box>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 3,
              }}
            >
              <Typography variant="h2">Your Kanban Boards</Typography>
              <Button variant="secondary" onClick={handleOpenCreateDialog}>
                + Create Board
              </Button>
            </Box>

            {boards.length === 0 ? (
              <Card>
                <Box sx={{ textAlign: "center", py: 6 }}>
                  <Typography variant="h3" gutterBottom>
                    No boards yet
                  </Typography>
                  <Typography color="text.secondary">
                    Create your first kanban board to get started!
                  </Typography>
                </Box>
              </Card>
            ) : (
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "repeat(auto-fill, minmax(300px, 1fr))",
                  gap: 3,
                }}
              >
                {boards.map(
                  (board: NonNullable<GetMeAndBoardsQuery["boards"]>[0]) => (
                    <Card
                      key={board.externalId}
                      onClick={() => router.push(`/board/${encodeGuidToUrlSafeBase64(board.externalId)}`)}
                      sx={{
                        cursor: "pointer",
                        transition: "box-shadow 0.2s",
                        "&:hover": {
                          boxShadow: 4,
                        },
                      }}
                    >
                      <Typography variant="h3" gutterBottom>
                        {board.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Created:{" "}
                        {new Date(board.createdOn).toLocaleDateString()}
                      </Typography>
                    </Card>
                  )
                )}
              </Box>
            )}
          </Box>
        </Stack>

        {/* Create Board Dialog */}
        <CreateBoardDialog
          open={isCreateDialogOpen}
          onClose={handleCloseCreateDialog}
          onBoardCreated={handleBoardCreated}
          authToken={authToken}
          currentUserId={data?.me?.externalId || "unknown"}
        />
      </Container>
    </ThemeProvider>
  );
}
