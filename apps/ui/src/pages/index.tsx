import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

export default function Home() {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    // Only run on client side after component is mounted
    if (!mounted) return;

    const token = localStorage.getItem('authToken');
    
    if (token) {
      // User has a token, redirect to home page
      console.log('Token found, redirecting to /home');
      router.push('/home');
    } else {
      // No token, redirect to auth
      console.log('No token found, redirecting to auth');
      const authUrl = process.env.NEXT_PUBLIC_AUTH_URL || (() => { throw new Error('NEXT_PUBLIC_AUTH_URL environment variable is required') })();
      window.location.href = authUrl;
    }
  }, [mounted, router]);

  // Show loading while checking authentication
  if (!mounted) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        fontFamily: 'Arial, sans-serif'
      }}>
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <p>Redirecting...</p>
    </div>
  );
}