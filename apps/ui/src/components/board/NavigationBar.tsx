import React from 'react';
import { useRouter } from 'next/router';
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  Divider,
  Brightness4,
  Brightness7,
  type PaletteMode,
} from '@kanban/design-system';

interface NavigationBarProps {
  themeMode: PaletteMode;
  onThemeToggle: () => void;
  anchorEl: HTMLElement | null;
  onMenuOpen: (event: React.MouseEvent<HTMLElement>) => void;
  onMenuClose: () => void;
  onLogout: () => void;
}

const NavigationBar: React.FC<NavigationBarProps> = ({
  themeMode,
  onThemeToggle,
  anchorEl,
  onMenuOpen,
  onMenuClose,
  onLogout,
}) => {
  const router = useRouter();
  const open = Boolean(anchorEl);

  return (
    <AppBar position="static" sx={{ zIndex: 1000 }}>
      <Toolbar>
        <Typography
          variant="h6"
          component="div"
          sx={{ 
            flexGrow: 1, 
            cursor: "pointer",
            "&:hover": {
              opacity: 0.8,
            },
          }}
          onClick={() => router.push("/dashboard")}
        >
          Kanban Board
        </Typography>
        
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {/* Theme Toggle Button */}
          <IconButton
            onClick={onThemeToggle}
            color="inherit"
            sx={{ mr: 1 }}
          >
            {themeMode === 'dark' ? <Brightness7 /> : <Brightness4 />}
          </IconButton>
          
          {/* Profile Avatar */}
          <Avatar
            sx={{ 
              bgcolor: "secondary.main", 
              cursor: "pointer",
              "&:hover": {
                opacity: 0.8,
              },
            }}
            onClick={onMenuOpen}
          >
            U
          </Avatar>
          
          <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={onMenuClose}
            onClick={onMenuClose}
            PaperProps={{
              elevation: 0,
              sx: {
                overflow: "visible",
                filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
                mt: 1.5,
                "& .MuiAvatar-root": {
                  width: 32,
                  height: 32,
                  ml: -0.5,
                  mr: 1,
                },
                "&:before": {
                  content: '""',
                  display: "block",
                  position: "absolute",
                  top: 0,
                  right: 14,
                  width: 10,
                  height: 10,
                  bgcolor: "background.paper",
                  transform: "translateY(-50%) rotate(45deg)",
                  zIndex: 0,
                },
              },
            }}
            transformOrigin={{ horizontal: "right", vertical: "top" }}
            anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
          >
            <MenuItem onClick={() => router.push("/profile")}>
              <Avatar /> Profile
            </MenuItem>
            <MenuItem onClick={() => router.push("/dashboard")}>
              Dashboard
            </MenuItem>
            <Divider />
            <MenuItem onClick={onLogout}>
              Logout
            </MenuItem>
          </Menu>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default NavigationBar;
