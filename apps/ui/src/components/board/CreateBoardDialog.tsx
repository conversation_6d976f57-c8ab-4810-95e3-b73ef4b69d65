import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
} from "@kanban/design-system";
import { 
  useCreateBoardMutation,
  useCreateBoardLinkedToCardMutation,
  ColumnType
} from "@kanban/graphql-schema/dist/client";
import { v4 as uuidv4 } from "uuid";

interface CreateBoardDialogProps {
  open: boolean;
  onClose: () => void;
  onBoardCreated: () => void;
  authToken: string | null;
  currentUserId: string;
  // Optional props for linking to a card
  linkedCardId?: string;
  linkedCardTitle?: string;
}

const CreateBoardDialog: React.FC<CreateBoardDialogProps> = ({
  open,
  onClose,
  onBoardCreated,
  authToken,
  currentUserId,
  linkedCardId,
  linkedCardTitle,
}) => {
  const [boardName, setBoardName] = useState(linkedCardTitle || "");
  const [notStartedColumnName, setNotStartedColumnName] = useState("Not Started");
  const [inProgressColumnName, setInProgressColumnName] = useState("In Progress");
  const [completedColumnName, setCompletedColumnName] = useState("Completed");

  const [createBoard, { loading: creating }] = useCreateBoardMutation({
    context: {
      headers: {
        authorization: authToken ? `Bearer ${authToken}` : "",
      },
    },
  });

  const [createBoardLinkedToCard, { loading: creatingLinked }] = useCreateBoardLinkedToCardMutation({
    context: {
      headers: {
        authorization: authToken ? `Bearer ${authToken}` : "",
      },
    },
  });

  // Update board name when linkedCardTitle changes
  useEffect(() => {
    if (linkedCardTitle) {
      setBoardName(linkedCardTitle);
    }
  }, [linkedCardTitle]);

  const handleCreate = async () => {
    if (!boardName.trim()) return;

    try {
      // Generate IDs for board and columns
      const boardId = uuidv4();
      const notStartedColumnId = uuidv4();
      const inProgressColumnId = uuidv4();
      const completedColumnId = uuidv4();

      const boardInput = {
        externalId: boardId,
        name: boardName.trim(),
        columnOrder: [notStartedColumnId, inProgressColumnId, completedColumnId],
        columns: {
          create: [
            {
              node: {
                externalId: notStartedColumnId,
                name: notStartedColumnName.trim(),
                type: ColumnType.NotStarted,
                wipLimit: null,
                cardPriorityOrder: [],
              }
            },
            {
              node: {
                externalId: inProgressColumnId,
                name: inProgressColumnName.trim(),
                type: ColumnType.InProgress,
                wipLimit: null,
                cardPriorityOrder: [],
              }
            },
            {
              node: {
                externalId: completedColumnId,
                name: completedColumnName.trim(),
                type: ColumnType.Completed,
                wipLimit: null,
                cardPriorityOrder: [],
              }
            }
          ]
        },
        createdBy: currentUserId,
        createdOn: new Date().toISOString(),
      };

      let boardResult;

      if (linkedCardId) {
        // Create board and link it to the card
        boardResult = await createBoardLinkedToCard({
          variables: {
            boardInput,
            cardId: linkedCardId,
            boardId: boardId,
          },
        });
      } else {
        // Create board normally
        boardResult = await createBoard({
          variables: {
            input: [boardInput],
          },
        });
      }

      if (boardResult.data?.createBoards?.boards?.[0]) {
        // Reset form and close dialog
        setBoardName("");
        setNotStartedColumnName("Not Started");
        setInProgressColumnName("In Progress");
        setCompletedColumnName("Completed");
        onClose();
        
        // Notify parent to refetch boards
        onBoardCreated();
      }
    } catch (error) {
      console.error("Error creating board and columns:", error);
    }
  };

  const handleClose = () => {
    setBoardName("");
    setNotStartedColumnName("Not Started");
    setInProgressColumnName("In Progress");
    setCompletedColumnName("Completed");
    onClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleCreate();
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        {linkedCardId ? `Create Board for "${linkedCardTitle}"` : "Create New Board"}
      </DialogTitle>
      <DialogContent>
        <TextField
          autoFocus
          fullWidth
          label="Board Name"
          value={boardName}
          onChange={(e) => setBoardName(e.target.value)}
          onKeyDown={handleKeyDown}
          sx={{ mt: 2, mb: 3 }}
        />
        
        <Typography variant="h6" sx={{ mb: 2 }}>
          Default Columns
        </Typography>
        
        <TextField
          fullWidth
          label="Not Started Column Name"
          value={notStartedColumnName}
          onChange={(e) => setNotStartedColumnName(e.target.value)}
          onKeyDown={handleKeyDown}
          sx={{ mb: 2 }}
        />
        
        <TextField
          fullWidth
          label="In Progress Column Name"
          value={inProgressColumnName}
          onChange={(e) => setInProgressColumnName(e.target.value)}
          onKeyDown={handleKeyDown}
          sx={{ mb: 2 }}
        />
        
        <TextField
          fullWidth
          label="Completed Column Name"
          value={completedColumnName}
          onChange={(e) => setCompletedColumnName(e.target.value)}
          onKeyDown={handleKeyDown}
        />
      </DialogContent>
      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleClose}>Cancel</Button>
        <Button
          variant="primary"
          onClick={handleCreate}
          disabled={
            !boardName.trim() || 
            !notStartedColumnName.trim() || 
            !inProgressColumnName.trim() || 
            !completedColumnName.trim() || 
            creating || creatingLinked
          }
        >
          {(creating || creatingLinked) ? "Creating..." : "Create Board"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CreateBoardDialog;