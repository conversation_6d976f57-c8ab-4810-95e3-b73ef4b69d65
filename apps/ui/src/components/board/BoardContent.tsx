import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  closestCorners,
} from "@dnd-kit/core";
import { Box } from "@kanban/design-system";
import { Board } from "@kanban/graphql-schema/dist/client";
import React from "react";
import BoardColumn from "../column/BoardColumn";
import SortableCard from "../card/SortableCard";

interface ColumnDimensions {
  width: number;
  needsScroll: boolean;
}

interface BoardContentProps {
  boardData: Board;
  sensors: any;
  activeId: string | null;
  overId: string | null;
  collapsedColumns: Set<string>;
  columnDimensions: ColumnDimensions;
  onDragStart: (event: DragStartEvent) => void;
  onDragOver: (event: DragOverEvent) => void;
  onDragEnd: (event: DragEndEvent) => void;
  onColumnToggle: (columnId: string) => void;
  onCardCreated: () => void;
  onUpdateCard?: (cardId: string, title: string, description: string) => void;
  onDeleteCard?: (cardId: string) => void;
}

const BoardContent: React.FC<BoardContentProps> = ({
  boardData,
  sensors,
  activeId,
  overId,
  collapsedColumns,
  columnDimensions,
  onDragStart,
  onDragOver,
  onDragEnd,
  onColumnToggle,
  onCardCreated,
  onUpdateCard,
  onDeleteCard,
}) => {
  return (
    <Box
      sx={{
        flexGrow: 1,
        p: 1,
        overflow: "auto",
      }}
    >
      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={onDragStart}
        onDragOver={onDragOver}
        onDragEnd={onDragEnd}
      >
        <Box
          sx={{
            display: "flex",
            gap: 1,
            minWidth: "fit-content",
            height: "calc(100vh - 200px)", // Full viewport height minus headers/footers
            ...(columnDimensions.needsScroll && {
              overflowX: "auto",
            }),
          }}
        >
          {boardData.columns.map((column) => {
            const isCollapsed = collapsedColumns.has(column.externalId);

            return (
              <BoardColumn
                key={column.externalId}
                column={column}
                isCollapsed={isCollapsed}
                width={columnDimensions.width}
                activeId={activeId}
                onDoubleClick={() => onColumnToggle(column.externalId)}
                onCardCreated={onCardCreated}
                onUpdateCard={onUpdateCard}
                onDeleteCard={onDeleteCard}
                overId={overId}
              />
            );
          })}
        </Box>

        {/* Drag Overlay - shows the card being dragged */}
        <DragOverlay
          style={{
            cursor: "grabbing",
          }}
        >
          {(() => {
            if (!activeId || !boardData.columns) return null;

            // Find the active card within the columns
            const activeCard = boardData.columns
              .flatMap((column) => column.cards)
              .find((card) => card.externalId === activeId);

            if (!activeCard || !activeCard.externalId) return null;

            return (
              <Box
                sx={{
                  transform: "rotate(2deg)",
                  boxShadow: 0,
                  opacity: 0.9,
                  mx: 0, // Match the card margin
                }}
              >
                <SortableCard
                  id={activeCard.externalId as string}
                  title={activeCard.title || ""}
                  description={activeCard.description || ""}
                  isDragging={true}
                />
              </Box>
            );
          })()}
        </DragOverlay>
      </DndContext>
    </Box>
  );
};
export default BoardContent;
