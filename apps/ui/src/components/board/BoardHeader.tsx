import React from 'react';
import { Box, Typography } from '@kanban/design-system';

interface BoardHeaderProps {
  boardName: string;
}

const BoardHeader: React.FC<BoardHeaderProps> = ({ boardName }) => {
  return (
    <Box
      sx={{
        py: 2,
        px: 3,
        backgroundColor: "background.paper",
        borderBottom: 2,
        borderColor: "divider",
      }}
    >
      <Typography variant="h4" sx={{ textAlign: "left" }}>
        {boardName}
      </Typography>
    </Box>
  );
};

export default BoardHeader;
