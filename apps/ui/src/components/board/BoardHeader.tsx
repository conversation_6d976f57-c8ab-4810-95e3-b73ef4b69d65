import React, { useState } from "react";
import {
  Box,
  Typography,
  HoverButton,
  KeyboardArrowUp,
} from "@kanban/design-system";

interface BoardHeaderProps {
  boardName: string;
  sourceBoard?: { externalId: string; name: string } | null;
  onNavigateToSourceBoard?: (boardId: string) => void;
}

const BoardHeader: React.FC<BoardHeaderProps> = ({
  boardName,
  sourceBoard,
  onNavigateToSourceBoard,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleNavigateToSource = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (sourceBoard && onNavigateToSourceBoard) {
      onNavigateToSourceBoard(sourceBoard.externalId);
    }
  };

  const handleMouseEnter = () => setIsHovered(true);
  const handleMouseLeave = () => setIsHovered(false);
  return (
    <Box
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      sx={{
        py: 2,
        px: 3,
        backgroundColor: "background.paper",
        borderBottom: 2,
        borderColor: "divider",
        position: "relative", // Enable absolute positioning for hover button
      }}
    >
      <Typography variant="h4" sx={{ textAlign: "left" }}>
        {boardName}
      </Typography>

      {/* Navigation button to source board - appears on hover */}
      {sourceBoard && isHovered && (
        <HoverButton
          onClick={handleNavigateToSource}
          position="top-center"
          withBackground={true}
          animationName="fadeInScale"
          sx={{
            top: "50%",
            transform: "translateY(-50%)",
          }}
        >
          <KeyboardArrowUp fontSize="small" />
        </HoverButton>
      )}
    </Box>
  );
};

export default BoardHeader;
