import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  <PERSON>alogContent,
  DialogActions,
  TextField,
  Box,
  Typography,
  Button,
} from "@kanban/design-system";
import { useCreateCardMutation } from "@kanban/graphql-schema/dist/client";
import { useAuth } from "../../contexts/AuthContext";
import { v4 as uuidv4 } from "uuid";

interface AddCardDialogProps {
  open: boolean;
  onClose: () => void;
  onCardCreated: () => void; // Callback to refresh board data
  columnId: string;
  columnName: string;
  currentCardPriorityOrder: string[];
}

const AddCardDialog: React.FC<AddCardDialogProps> = ({
  open,
  onClose,
  onCardCreated,
  columnId,
  columnName,
  currentCardPriorityOrder,
}) => {
  const { user, authToken } = useAuth();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [titleError, setTitleError] = useState("");

  const [createCard, { loading: creating }] = useCreateCardMutation({
    context: {
      headers: {
        authorization: authToken ? `Bearer ${authToken}` : "",
      },
    },
  });

  const handleClose = () => {
    // Reset form when closing
    setTitle("");
    setDescription("");
    setTitleError("");
    onClose();
  };

  const handleSubmit = async () => {
    // Validate title is not empty
    if (!title.trim()) {
      setTitleError("Title is required");
      return;
    }

    // Clear any previous errors
    setTitleError("");

    try {
      // Generate new card ID
      const newCardId = uuidv4();

      // Add new card ID to the end of the current card priority order
      const updatedCardPriorityOrder = [...currentCardPriorityOrder, newCardId];

      // Create the card via API
      const result = await createCard({
        variables: {
          columnId: columnId,
          cardInput: {
            externalId: newCardId,
            title: title.trim(),
            description: description.trim() || null,
            tags: [],
            boardTags: [],
            color: null,
            priority: null,
            estimatedHours: null,
            workedHours: null,
            dueDate: null,
            createdBy: user?.externalId || "",
            createdOn: new Date().toISOString(),
          },
          updatedCardPriorityOrder: updatedCardPriorityOrder,
        },
      });

      if (result.data?.updateColumns?.columns?.[0]?.cards) {
        // Notify parent to refresh board data
        onCardCreated();

        // Reset form and close dialog
        setTitle("");
        setDescription("");
        handleClose();
      }
    } catch (error) {
      console.error("Error creating card:", error);
      setTitleError("Failed to create card. Please try again.");
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && event.ctrlKey) {
      handleSubmit();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
        },
      }}
    >
      <DialogTitle>
        <Typography variant="h6" component="div">
          Add Card to {columnName}
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ display: "flex", flexDirection: "column", gap: 3, pt: 1 }}>
          <TextField
            autoFocus
            label="Card Title"
            placeholder="Enter a title for this card..."
            value={title}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              setTitle(e.target.value);
              if (titleError) setTitleError(""); // Clear error when user types
            }}
            onKeyPress={handleKeyPress}
            error={!!titleError}
            helperText={titleError}
            fullWidth
            variant="outlined"
            required
          />

          <TextField
            label="Description"
            placeholder="Add a more detailed description..."
            value={description}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setDescription(e.target.value)
            }
            onKeyPress={handleKeyPress}
            fullWidth
            multiline
            rows={4}
            variant="outlined"
          />
        </Box>
      </DialogContent>

      <DialogActions
        sx={{
          px: 3,
          pb: 3,
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="caption" color="text.secondary">
          Tip: Press Ctrl+Enter to quickly add the card
        </Typography>
        <Box sx={{ display: "flex", gap: 1 }}>
          <Button onClick={handleClose} variant="ghost">
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="primary"
            disabled={!title.trim() || creating}
          >
            {creating ? "Creating..." : "Add Card"}
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default AddCardDialog;
