import { useDroppable } from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import {
  Box,
  Button,
  Divider,
  Stack,
  Typography,
  Menu,
  MenuItem,
} from "@kanban/design-system";
import { Column, ColumnType } from "@kanban/graphql-schema/dist/client";
import React, { useMemo, useState } from "react";
import AddCardDialog from "../board/AddCardDialog";
import CardDetailsDialog from "../card/CardDetailsDialog";
import CreateBoardDialog from "../board/CreateBoardDialog";
import SortableCard from "../card/SortableCard";
import { useAuth } from "../../contexts/AuthContext";

interface BoardColumnProps {
  column: Column;
  isCollapsed: boolean;
  width: number;
  activeId: string | null;
  onDoubleClick: () => void;
  onCardCreated: () => void;
  onUpdateCard?: (cardId: string, title: string, description: string) => void;
  onDeleteCard?: (cardId: string) => void;
  overId?: string | null;
}

// Drop indicator component
const DropIndicator: React.FC<{ show: boolean }> = ({ show }) => {
  if (!show) return null;

  return (
    <Divider
      sx={{
        mx: 2,
        my: 0.5,
        borderWidth: 2,
        borderColor: "primary.main",
        backgroundColor: "primary.main",
        opacity: 0.8,
        transition: "all 0.2s ease",
      }}
    />
  );
};

// Droppable "Add Card" button for empty columns
const DroppableAddButton: React.FC<{
  columnId: string;
  isEmpty: boolean;
  onClick: () => void;
}> = ({ columnId, isEmpty, onClick }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: isEmpty ? columnId : `add-button-${columnId}`,
  });

  return (
    <Button
      ref={setNodeRef}
      variant="ghost"
      onClick={onClick}
      sx={{
        minHeight: 60,
        border: "2px dashed",
        borderColor: isOver && isEmpty ? "primary.main" : "divider",
        backgroundColor: isOver && isEmpty ? "action.hover" : "transparent",
        mx: 2, // Add horizontal margin to match header padding
        transition: "all 0.2s ease",
        "&:hover": {
          borderColor: "primary.main",
          backgroundColor: "action.hover",
        },
      }}
    >
      {isOver && isEmpty ? "Drop card here" : "+ Add a card"}
    </Button>
  );
};

const BoardColumn: React.FC<BoardColumnProps> = ({
  column,
  isCollapsed,
  width,
  activeId,
  onDoubleClick,
  onCardCreated,
  onUpdateCard,
  onDeleteCard,
  overId,
}) => {
  const { authToken, user } = useAuth();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isCardDetailsOpen, setIsCardDetailsOpen] = useState(false);
  const [selectedCard, setSelectedCard] = useState<any>(null);
  const [menuAnchorEl, setMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [menuCardId, setMenuCardId] = useState<string | null>(null);
  const [isCreateBoardDialogOpen, setIsCreateBoardDialogOpen] = useState(false);
  const [linkedCard, setLinkedCard] = useState<any>(null);

  const handleOpenAddDialog = () => {
    setIsAddDialogOpen(true);
  };

  const handleCloseAddDialog = () => {
    setIsAddDialogOpen(false);
  };

  const handleCardTitleClick = (cardId: string) => {
    const card = column.cards.find((c) => c.externalId === cardId);
    if (card) {
      setSelectedCard(card);
      setIsCardDetailsOpen(true);
    }
  };

  const handleCloseCardDetails = () => {
    setIsCardDetailsOpen(false);
    setSelectedCard(null);
  };

  const handleMenuClick = (cardId: string, anchorEl: HTMLElement) => {
    setMenuCardId(cardId);
    setMenuAnchorEl(anchorEl);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setMenuCardId(null);
  };

  const handleCardBoardClick = (cardId: string) => {
    const card = column.cards.find((c) => c.externalId === cardId);
    if (card) {
      setLinkedCard(card);
      setIsCreateBoardDialogOpen(true);
    }
  };

  const handleCloseCreateBoardDialog = () => {
    setIsCreateBoardDialogOpen(false);
    setLinkedCard(null);
  };

  const handleBoardCreated = () => {
    // TODO: Implement linking logic here when GraphQL mutation is ready
    // For now, just refresh the parent
    onCardCreated();
  };

  const handleEditCard = () => {
    if (menuCardId) {
      const card = column.cards.find((c) => c.externalId === menuCardId);
      if (card) {
        setSelectedCard(card);
        setIsCardDetailsOpen(true);
      }
    }
    handleMenuClose();
  };

  const handleDeleteCard = () => {
    if (menuCardId && onDeleteCard) {
      onDeleteCard(menuCardId);
    }
    handleMenuClose();
  };

  const cardIds = useMemo(
    () => column.cards.map((card) => card.externalId),
    [column.cards]
  );

  return (
    <Box
      sx={{
        width: isCollapsed ? 60 : width,
        minWidth: isCollapsed ? 60 : width,
        maxWidth: isCollapsed ? 60 : width,
        height: "100%",
        display: "flex",
        flexDirection: "column",
        transition: "width 0.3s ease",
        backgroundColor: "background.secondary",
        px: 1,
      }}
    >
      {/* Column Header */}
      <Box
        onDoubleClick={onDoubleClick}
        sx={{
          mx: -1,
          py: 0.5,
          px: 1,
          borderBottom: isCollapsed ? "none" : 2,
          borderRight: isCollapsed ? 2 : "none",
          borderColor: "primary.main",
          backgroundColor: "background.paper",
          display: "flex",
          justifyContent: isCollapsed ? "center" : "space-between",
          alignItems: "center",
          cursor: "pointer",
          userSelect: "none",
          height: isCollapsed ? "100%" : "auto",
          minHeight: isCollapsed ? "100%" : "auto",
        }}
      >
        {isCollapsed ? (
          <Typography
            variant="h6"
            sx={{
              writingMode: "vertical-rl",
              textOrientation: "mixed",
              transform: "rotate(180deg)",
              whiteSpace: "nowrap",
            }}
          >
            {column.name}
          </Typography>
        ) : (
          <>
            <Typography variant="h6">{column.name}</Typography>
            <Typography variant="body2" color="text.secondary">
              {cardIds.length}
            </Typography>
          </>
        )}
      </Box>

      {/* Column Cards - Only show when not collapsed */}
      {!isCollapsed && (
        <Box
          sx={{
            flexGrow: 1, // Take up remaining vertical space
            height: "100%",
            overflow: "auto", // Allow scrolling if content exceeds height
            pt: 1, // Top padding for spacing from header
          }}
        >
          <SortableContext
            items={cardIds}
            strategy={verticalListSortingStrategy}
          >
            <Stack spacing={1} sx={{ px: 0 }}>
              {/* Show drop indicator at the beginning if dragging over column but not over any specific card */}
              <DropIndicator
                show={
                  !!activeId &&
                  overId === column.externalId &&
                  column.cards.length > 0
                }
              />

              {/* Render cards in priority order if available, otherwise use cards array directly */}
              {(column.cardPriorityOrder && column.cardPriorityOrder.length > 0
                ? column.cardPriorityOrder
                    .map((cardId) =>
                      column.cards.find((c) => c.externalId === cardId)
                    )
                    .filter(Boolean)
                : column.cards
              ).map((card, index) => {
                if (!card) return null;

                const isBeingDraggedOver = overId === card.externalId;
                const isDifferentColumn =
                  activeId &&
                  column.cards.find((c) => c.externalId == activeId)?.column
                    ?.externalId !== column.externalId;

                return (
                  <React.Fragment key={card.externalId}>
                    <SortableCard
                      id={card.externalId}
                      title={card.title}
                      description={card.description || ""}
                      isDragging={activeId === card.externalId}
                      onTitleClick={handleCardTitleClick}
                      onMenuClick={handleMenuClick}
                      onCardBoardClick={handleCardBoardClick}
                    />

                    {/* Show drop indicator after this card if it's being dragged over and it's from a different column */}
                    <DropIndicator
                      show={isBeingDraggedOver && !!isDifferentColumn}
                    />
                  </React.Fragment>
                );
              })}

              {/* Droppable Add Card Button - Only show for NotStarted columns */}
              {column.type === ColumnType.NotStarted && (
                <DroppableAddButton
                  columnId={column.externalId}
                  isEmpty={cardIds.length === 0}
                  onClick={handleOpenAddDialog}
                />
              )}
            </Stack>
          </SortableContext>
        </Box>
      )}

      <AddCardDialog
        open={isAddDialogOpen}
        onClose={handleCloseAddDialog}
        onCardCreated={onCardCreated}
        columnId={column.externalId}
        columnName={column.name}
        currentCardPriorityOrder={column.cardPriorityOrder || []}
      />

      <CardDetailsDialog
        open={isCardDetailsOpen}
        onClose={handleCloseCardDetails}
        card={selectedCard}
        onUpdateCard={onUpdateCard}
        onDeleteCard={onDeleteCard}
        columnName={column.name}
      />

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem onClick={handleEditCard}>Edit Card</MenuItem>
        <MenuItem onClick={handleDeleteCard}>Delete Card</MenuItem>
      </Menu>

      {/* Create Board Dialog */}
      <CreateBoardDialog
        open={isCreateBoardDialogOpen}
        onClose={handleCloseCreateBoardDialog}
        onBoardCreated={handleBoardCreated}
        authToken={authToken}
        currentUserId={user?.externalId || ""}
        linkedCardId={linkedCard?.externalId}
        linkedCardTitle={linkedCard?.title}
      />
    </Box>
  );
};

export default BoardColumn;
