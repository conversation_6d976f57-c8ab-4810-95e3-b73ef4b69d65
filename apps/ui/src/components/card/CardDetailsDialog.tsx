import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Box,
  Typography,
  Button,
  Divider,
  Tabs,
  Tab,
  Paper,
  List,
  ListItem,
  ListItemText,
  Chip,
  CircularProgress,
} from "@kanban/design-system";
import { useGetCardWithDetailsQuery } from "@kanban/graphql-schema/dist/client";
import { useAuth } from "../../contexts/AuthContext";

interface Card {
  externalId: string;
  title: string;
  description: string;
}

interface CardDetailsDialogProps {
  open: boolean;
  onClose: () => void;
  card: Card | null;
  onUpdateCard?: (cardId: string, title: string, description: string) => void;
  onDeleteCard?: (cardId: string) => void;
  columnName?: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel({ children, value, index, ...other }: TabPanelProps) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`card-tabpanel-${index}`}
      aria-labelledby={`card-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 2 }}>{children}</Box>}
    </div>
  );
}

const CardDetailsDialog: React.FC<CardDetailsDialogProps> = ({
  open,
  onClose,
  card,
  onUpdateCard,
  onDeleteCard,
  columnName,
}) => {
  const { authToken } = useAuth();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [titleError, setTitleError] = useState("");
  const [tabValue, setTabValue] = useState(0);
  const [newComment, setNewComment] = useState("");

  // GraphQL query for detailed card data
  const { data, loading, error, refetch } = useGetCardWithDetailsQuery({
    variables: { cardId: card?.externalId || "" },
    skip: !card?.externalId || !open,
    context: {
      headers: {
        authorization: authToken ? `Bearer ${authToken}` : "",
      },
    },
  });

  const cardDetails = data?.columns?.[0]?.cards?.[0];
  const columnDetails = data?.columns?.[0];

  // Update form fields when card or cardDetails changes
  useEffect(() => {
    if (cardDetails) {
      setTitle(cardDetails.title);
      setDescription(cardDetails.description || "");
      setIsEditing(false);
      setTitleError("");
    } else if (card) {
      setTitle(card.title);
      setDescription(card.description || "");
      setIsEditing(false);
      setTitleError("");
    }
  }, [card, cardDetails]);

  const handleClose = () => {
    // Reset editing state when closing
    setIsEditing(false);
    setTitleError("");
    if (card) {
      setTitle(card.title);
      setDescription(card.description);
    }
    onClose();
  };

  const handleSave = () => {
    // Validate title is not empty
    if (!title.trim()) {
      setTitleError("Title is required");
      return;
    }

    // Clear any previous errors
    setTitleError("");

    // Call the onUpdateCard callback if available
    if (onUpdateCard && card) {
      onUpdateCard(card.externalId, title.trim(), description.trim());
    }

    // Exit editing mode
    setIsEditing(false);
  };

  const handleCancel = () => {
    // Reset to original values
    if (card) {
      setTitle(card.title);
      setDescription(card.description);
    }
    setTitleError("");
    setIsEditing(false);
  };

  const handleDelete = () => {
    if (onDeleteCard && card) {
      onDeleteCard(card.externalId);
      handleClose();
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && event.ctrlKey && isEditing) {
      handleSave();
    }
    if (event.key === "Escape" && isEditing) {
      handleCancel();
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleAddComment = () => {
    if (newComment.trim()) {
      // TODO: Implement add comment functionality
      console.log("Adding comment:", newComment);
      setNewComment("");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (!card) return null;

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 2,
            minHeight: 600,
            height: "80vh",
          },
        },
      }}
    >
      <DialogTitle>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "flex-start",
          }}
        >
          <Box sx={{ flex: 1 }}>
            {isEditing ? (
              <TextField
                autoFocus
                value={title}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  setTitle(e.target.value);
                  if (titleError) setTitleError("");
                }}
                onKeyPress={handleKeyPress}
                error={!!titleError}
                helperText={titleError}
                fullWidth
                variant="outlined"
                placeholder="Enter card title..."
                required
                sx={{ mt: 1 }}
              />
            ) : (
              <Typography
                variant="h5"
                component="div"
                sx={{ lineHeight: 1.2, mr: 2 }}
              >
                {cardDetails?.title || card.title}
              </Typography>
            )}
          </Box>
          {(columnName || columnDetails?.name) && (
            <Chip
              label={columnName || columnDetails?.name}
              size="small"
              variant="outlined"
              sx={{ mt: 1 }}
            />
          )}
        </Box>
      </DialogTitle>

      <DialogContent sx={{ height: "100%", overflow: "hidden" }}>
        {loading ? (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: 200,
            }}
          >
            <CircularProgress />
          </Box>
        ) : error ? (
          <Box sx={{ textAlign: "center", py: 4 }}>
            <Typography color="error">
              Error loading card details: {error.message}
            </Typography>
            <Button onClick={() => refetch()} sx={{ mt: 2 }}>
              Retry
            </Button>
          </Box>
        ) : (
          <Box sx={{ display: "flex", height: "100%", gap: 3 }}>
            {/* Left Side - Description and Discussion (2/3 width) */}
            <Box
              sx={{
                flex: 2,
                display: "flex",
                flexDirection: "column",
                gap: 3,
                overflow: "auto",
              }}
            >
              {/* Description Section */}
              <Paper>
                <Typography variant="h6" gutterBottom>
                  Description
                </Typography>
                {isEditing ? (
                  <Box>
                    <TextField
                      value={description}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        setDescription(e.target.value)
                      }
                      onKeyPress={handleKeyPress}
                      fullWidth
                      multiline
                      rows={6}
                      variant="outlined"
                      placeholder="Add a description..."
                    />
                  </Box>
                ) : (
                  <Typography
                    variant="body1"
                    sx={{ minHeight: 100, whiteSpace: "pre-wrap" }}
                  >
                    {cardDetails?.description || card.description || (
                      <span style={{ fontStyle: "italic", opacity: 0.7 }}>
                        No description provided
                      </span>
                    )}
                  </Typography>
                )}
              </Paper>

              {/* Discussion Section */}
              <Paper sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
                <Typography variant="h6" gutterBottom>
                  Discussion
                </Typography>

                {/* Comments List */}
                <Box sx={{ flex: 1, overflow: "auto", mb: 2 }}>
                  {cardDetails?.commentList?.comments &&
                  cardDetails.commentList.comments.length > 0 ? (
                    <List sx={{ py: 0 }}>
                      {cardDetails.commentList.comments.map((comment) => (
                        <ListItem
                          key={comment.externalId}
                          alignItems="flex-start"
                          sx={{ px: 0 }}
                        >
                          <ListItemText
                            primary={
                              <Typography variant="body2">
                                {comment.content}
                              </Typography>
                            }
                            secondary={
                              <Typography
                                variant="caption"
                                color="text.secondary"
                              >
                                {formatDate(comment.createdOn)}
                                {comment.modifiedOn &&
                                  comment.modifiedOn !== comment.createdOn &&
                                  ` (edited ${formatDate(comment.modifiedOn)})`}
                              </Typography>
                            }
                          />
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ fontStyle: "italic", textAlign: "center", py: 2 }}
                    >
                      No comments yet. Start the discussion!
                    </Typography>
                  )}
                </Box>

                {/* Add Comment */}
                <Box
                  sx={{
                    display: "flex",
                    gap: 1,
                    alignItems: "flex-end",
                  }}
                >
                  <TextField
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    placeholder="Add a comment..."
                    multiline
                    maxRows={3}
                    fullWidth
                    variant="outlined"
                    size="small"
                  />
                  <Button
                    onClick={handleAddComment}
                    variant="primary"
                    disabled={!newComment.trim()}
                    sx={{ whiteSpace: "nowrap" }}
                  >
                    Add Comment
                  </Button>
                </Box>
              </Paper>
            </Box>

            {/* Right Side - Tabs (1/3 width) */}
            <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
              <Paper
                sx={{
                  height: "100%",
                  display: "flex",
                  flexDirection: "column",
                }}
              >
                <Tabs
                  value={tabValue}
                  onChange={handleTabChange}
                  sx={{ borderBottom: 1, borderColor: "divider" }}
                >
                  <Tab label="Attachments" />
                  <Tab label="Links" disabled />
                  <Tab label="History" />
                </Tabs>

                <Box sx={{ flex: 1, overflow: "auto" }}>
                  <TabPanel value={tabValue} index={0}>
                    {/* Attachments Tab */}
                    {cardDetails?.attachmentList?.attachments &&
                    cardDetails.attachmentList.attachments.length > 0 ? (
                      <List>
                        {cardDetails.attachmentList.attachments.map(
                          (attachment) => (
                            <ListItem key={attachment.externalId}>
                              <ListItemText
                                primary={attachment.name}
                                secondary={
                                  <Typography
                                    variant="caption"
                                    color="text.secondary"
                                  >
                                    Uploaded {formatDate(attachment.uploadedOn)}
                                  </Typography>
                                }
                              />
                            </ListItem>
                          )
                        )}
                      </List>
                    ) : (
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ textAlign: "center", pt: 2 }}
                      >
                        No attachments
                      </Typography>
                    )}
                  </TabPanel>

                  <TabPanel value={tabValue} index={1}>
                    {/* Links Tab - Coming Soon */}
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ textAlign: "center", pt: 2 }}
                    >
                      Links feature coming soon
                    </Typography>
                  </TabPanel>

                  <TabPanel value={tabValue} index={2}>
                    {/* History Tab */}
                    {cardDetails?.historyList?.histories &&
                    cardDetails.historyList.histories.length > 0 ? (
                      <List>
                        {cardDetails.historyList.histories.map((history) => (
                          <ListItem
                            key={history.externalId}
                            sx={{ px: 2, py: 1 }}
                          >
                            <ListItemText
                              primary={
                                <Typography variant="body2">
                                  {history.operation}
                                </Typography>
                              }
                              secondary={
                                <Box>
                                  <Typography
                                    variant="caption"
                                    color="text.secondary"
                                  >
                                    {formatDate(history.dateTime)}
                                  </Typography>
                                  {history.before && (
                                    <Typography
                                      variant="caption"
                                      display="block"
                                      color="text.secondary"
                                    >
                                      From: {history.before}
                                    </Typography>
                                  )}
                                  {history.after && (
                                    <Typography
                                      variant="caption"
                                      display="block"
                                      color="text.secondary"
                                    >
                                      To: {history.after}
                                    </Typography>
                                  )}
                                </Box>
                              }
                            />
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ textAlign: "center", pt: 2 }}
                      >
                        No history available
                      </Typography>
                    )}
                  </TabPanel>
                </Box>
              </Paper>
            </Box>
          </Box>
        )}

        {isEditing && (
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ mt: 2, display: "block" }}
          >
            Tip: Press Ctrl+Enter to save, Escape to cancel
          </Typography>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3, gap: 1 }}>
        {isEditing ? (
          <>
            <Button onClick={handleCancel} variant="ghost">
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              variant="primary"
              disabled={!title.trim()}
            >
              Save Changes
            </Button>
          </>
        ) : (
          <>
            {onDeleteCard && (
              <Button
                onClick={handleDelete}
                variant="ghost"
                sx={{
                  color: "error.main",
                  "&:hover": {
                    backgroundColor: "error.light",
                    color: "error.contrastText",
                  },
                }}
              >
                Delete Card
              </Button>
            )}
            <Box sx={{ flexGrow: 1 }} />
            <Button onClick={handleClose} variant="ghost">
              Close
            </Button>
            {onUpdateCard && (
              <Button onClick={() => setIsEditing(true)} variant="primary">
                Edit Card
              </Button>
            )}
          </>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default CardDetailsDialog;
