import React, { useState } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  Card,
  Typography,
  Box,
  HoverButton,
  MoreVert,
  Add,
} from "@kanban/design-system";

interface SortableCardProps {
  id: string;
  title: string;
  description: string;
  isDragging?: boolean;
  onTitleClick?: (cardId: string) => void;
  onMenuClick?: (cardId: string, anchorEl: HTMLElement) => void;
  onCardBoardClick?: (cardId: string) => void;
}

const SortableCard: React.FC<SortableCardProps> = ({
  id,
  title,
  description,
  isDragging,
  onTitleClick,
  onMenuClick,
  onCardBoardClick,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({ id });

  const style = {
    // Only apply transform if we're not actively dragging this card
    // This prevents the card from moving during drag, letting only the overlay move
    transform: isSortableDragging ? "none" : CSS.Transform.toString(transform),
    transition: isSortableDragging ? "none" : transition,
    opacity: isSortableDragging ? 0.5 : 1,
  };

  const handleTitleClick = (event: React.MouseEvent) => {
    // Prevent drag from starting when clicking on title
    event.stopPropagation();
    if (onTitleClick) {
      onTitleClick(id);
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    if (onMenuClick) {
      onMenuClick(id, event.currentTarget);
    }
  };

  const handleCardBoardClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (onCardBoardClick) {
      onCardBoardClick(id);
    }
  };

  const handleMouseEnter = () => setIsHovered(true);
  const handleMouseLeave = () => setIsHovered(false);

  return (
    <Box
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      sx={{
        position: "relative", // Enable absolute positioning for icons
        mx: 2, // Add horizontal margin to match header padding
      }}
    >
      <Card
        ref={setNodeRef}
        style={style}
        {...attributes}
        {...listeners}
        sx={{
          cursor: isDragging ? "grabbing" : "grab",
          maxHeight: "140px", // Limit card height
          overflow: "hidden", // Hide overflow content
          paddingBottom: "10px", // Reserve space for add icon
          ...(isDragging
            ? {
                transform: "none !important", // Disable transform during drag
                transition: "none !important", // Disable transition during drag
              }
            : {}),
          transition: "box-shadow 0.2s",
          display: "flex",
          flexDirection: "column",
          "&:hover": {
            boxShadow: 2,
          },
          "&:active": {
            cursor: "grabbing",
          },
        }}
      >
        <Box
          onClick={handleTitleClick}
          sx={{
            cursor: onTitleClick ? "pointer" : "inherit",
            display: "inline-block",
            width: "fit-content",
            "&:hover": onTitleClick
              ? {
                  textDecoration: "underline",
                }
              : {},
          }}
        >
          <Typography
            variant="body2"
            onClick={handleTitleClick}
            sx={{
              cursor: "pointer",
              "&:hover": {
                textDecoration: "underline",
              },
              fontSize: "0.75rem", // Even smaller font size
              lineHeight: 1.2,
            }}
          >
            {title}
          </Typography>
        </Box>

        {/* Menu Icon - Top Right Corner (appears on hover) */}
        {isHovered && (
          <HoverButton
            onClick={handleMenuClick}
            position="top-right"
            withBackground={true}
            animationName="fadeInScale"
          >
            <MoreVert fontSize="small" />
          </HoverButton>
        )}

        {/* Open /Create board dialog - Bottom Center (appears on hover) */}
        {isHovered && (
          <HoverButton
            onClick={handleCardBoardClick}
            position="bottom-center"
            withBackground={false}
            animationName="fadeInScaleCenter"
          >
            <Add fontSize="small" />
          </HoverButton>
        )}
      </Card>
    </Box>
  );
};

export default SortableCard;
