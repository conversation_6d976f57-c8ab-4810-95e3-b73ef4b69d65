import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/router';

interface User {
  externalId: string;
  email: string;
  name: string;
}

interface AuthContextType {
  user: User | null;
  authToken: string | null;
  isLoading: boolean;
  login: (token: string, userData: User) => void;
  logout: () => void;
  setUser: (user: User | null) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [authToken, setAuthToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Initialize auth state from localStorage
    const initializeAuth = () => {
      try {
        const token = localStorage.getItem('authToken');
        const userData = localStorage.getItem('userData');
        
        if (token) {
          setAuthToken(token);
          
          if (userData) {
            try {
              setUser(JSON.parse(userData));
            } catch (parseError) {
              console.error('Error parsing userData:', parseError);
              // Clear corrupted userData but keep the token
              localStorage.removeItem('userData');
            }
          } else {
            // Try to extract user info from JWT token
            try {
              const payload = JSON.parse(atob(token.split('.')[1]));
              if (payload.id && payload.email && payload.name) {
                const user = {
                  externalId: payload.id,
                  email: payload.email,
                  name: payload.name
                };
                setUser(user);
                // Save the extracted user data for future use
                localStorage.setItem('userData', JSON.stringify(user));
              }
            } catch (jwtError) {
              console.error('Error extracting user from JWT:', jwtError);
            }
          }
        }
      } catch (error) {
        console.error('Error initializing auth state:', error);
        // Clear potentially corrupted data
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = (token: string, userData: User) => {
    setAuthToken(token);
    setUser(userData);
    localStorage.setItem('authToken', token);
    localStorage.setItem('userData', JSON.stringify(userData));
  };

  const logout = () => {
    setAuthToken(null);
    setUser(null);
    localStorage.removeItem('authToken');
    localStorage.removeItem('userData');
    localStorage.removeItem('themeMode');
    router.push('/');
  };

  const value: AuthContextType = {
    user,
    authToken,
    isLoading,
    login,
    logout,
    setUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;