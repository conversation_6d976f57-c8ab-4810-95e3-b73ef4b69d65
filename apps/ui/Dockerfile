# Single-stage build for UI
FROM node:18-alpine

ENV CYPRESS_INSTALL_BINARY=0

# Install pnpm
RUN npm install -g pnpm

WORKDIR /app

# Accept build arguments
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_AUTH_URL

# Set environment variables from build args
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_AUTH_URL=$NEXT_PUBLIC_AUTH_URL

# Copy everything needed for the build
COPY package.json pnpm-*.yaml turbo.json ./
COPY apps/ui ./apps/ui
COPY packages ./packages

# Install dependencies
RUN pnpm install

# Build the shared packages first
RUN pnpm turbo run build --filter=@kanban/tsconfig
RUN pnpm turbo run build --filter=@kanban/graphql-schema

# Build the UI
RUN pnpm turbo run build --filter=@kanban/ui

WORKDIR /app/apps/ui

EXPOSE 3000

CMD ["pnpm", "start"]