{"name": "@kanban/ui", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev -p 3001", "lint": "next lint", "start": "next start", "clean": "rm -rf .next"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@kanban/design-system": "workspace:*", "@kanban/graphql-schema": "workspace:*"}, "devDependencies": {"@kanban/tsconfig": "workspace:*"}}