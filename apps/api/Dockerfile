# Single-stage build for API
FROM node:18-alpine

ENV CYPRESS_INSTALL_BINARY=0

# Install pnpm
RUN npm install -g pnpm

WORKDIR /app

# Copy everything needed for the build
COPY package.json pnpm-*.yaml turbo.json ./
COPY apps/api ./apps/api
COPY packages ./packages

# Install dependencies
RUN pnpm install

# Build the shared packages first
RUN pnpm turbo run build --filter=@kanban/tsconfig
RUN pnpm turbo run build --filter=@kanban/graphql-schema

# Build the API
RUN pnpm turbo run build --filter=@kanban/api

WORKDIR /app/apps/api

EXPOSE 4000

CMD ["node", "dist/index.js"]