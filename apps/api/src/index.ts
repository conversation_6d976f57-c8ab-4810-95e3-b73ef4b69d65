import { ApolloServer } from "@apollo/server";
import { expressMiddleware } from "@as-integrations/express5";
import express from "express";
import cors from "cors";
import { Neo4jGraphQL } from "@neo4j/graphql";
import neo4j from "neo4j-driver";
import { typeDefs } from "@kanban/graphql-schema";
import { resolvers } from "./resolvers";
import { createJwtDecoder } from "./auth/jwt";

const app = express();

async function startServer() {
  // Debug: Log all environment variables that start with NEO4J
  console.log("Debug - Environment variables:");
  Object.keys(process.env)
    .filter((key) => key.startsWith("NEO4J"))
    .forEach((key) =>
      console.log(
        `  ${key}:`,
        process.env[key] ? process.env[key].length : "[NOT SET]"
      )
    );

  // Check for required environment variables
  const NEO4J_URI = process.env.NEO4J_URI;
  const NEO4J_USER = process.env.NEO4J_USER;
  const NEO4J_PASSWORD = process.env.NEO4J_PASSWORD;
  const PORT = process.env.PORT || 4000;

  if (!NEO4J_URI) {
    throw new Error("NEO4J_URI environment variable is required");
  }
  if (!NEO4J_USER) {
    throw new Error("NEO4J_USER environment variable is required");
  }
  if (!NEO4J_PASSWORD) {
    throw new Error("NEO4J_PASSWORD environment variable is required");
  }

  const driver = neo4j.driver(
    NEO4J_URI,
    neo4j.auth.basic(NEO4J_USER, NEO4J_PASSWORD)
  );

  // Test the connection
  try {
    await driver.verifyConnectivity();
    console.log("✅ Neo4j connection verified successfully");
  } catch (error) {
    console.error(
      "❌ Neo4j connection failed:",
      error instanceof Error ? error.message : String(error)
    );
    throw error;
  }

  // Create Neo4jGraphQL instance
  const neoSchema = new Neo4jGraphQL({
    typeDefs,
    resolvers,
    driver,
    features: {
      authorization: {
        key:
          process.env.JWT_SECRET ||
          (() => {
            throw new Error("JWT_SECRET environment variable is required");
          })(),
      },
    },
  });

  // Build the schema
  const schema = await neoSchema.getSchema();

  // Create Apollo Server
  const server = new ApolloServer({
    schema,
    introspection: true,
  });

  await server.start();

  app.use(
    "/graphql",
    cors<cors.CorsRequest>({
      origin: process.env.CORS_ORIGIN
        ? process.env.CORS_ORIGIN.split(",")
        : [
            "http://localhost:3001", // UI app
            "http://localhost:3003", // Auth app
            "http://localhost:3000", // Cypress default baseUrl
          ],
      credentials: true,
    }),
    express.json(),
    expressMiddleware(server, {
      context: async ({ req }) => {
        const token = req.headers.authorization?.replace("Bearer ", "");
        let user = null;

        if (token) {
          try {
            const decoded = createJwtDecoder()(token);
            user = decoded;
          } catch (error) {
            console.log("Invalid token:", error);
          }
        }

        return {
          driver,
          user,
          req,
        };
      },
    })
  );

  app.get("/", (req, res) => {
    res.send("Kanban API is running! GraphQL endpoint: /graphql");
  });

  app.get("/health", (req, res) => {
    res.json({ status: "healthy", timestamp: new Date().toISOString() });
  });

  app.listen(PORT, () => {
    console.log(`🚀 Kanban API ready at http://localhost:${PORT}`);
    console.log(`📊 GraphQL endpoint: http://localhost:${PORT}/graphql`);
  });

  // Graceful shutdown
  process.on("SIGINT", async () => {
    console.log("Shutting down gracefully...");
    await driver.close();
    process.exit(0);
  });
}

startServer().catch((error) => {
  console.error("Error starting server:", error);
  process.exit(1);
});
