import jwt from "jsonwebtoken";
import { User } from "@kanban/graphql-schema";

export interface JwtPayload {
  id: string;
  email: string;
  name: string;
  iat?: number;
  exp?: number;
}

export function createJwtToken(user: Pick<User, 'externalId' | 'email' | 'name'>): string {
  const secret = process.env.JWT_SECRET || (() => { throw new Error('JWT_SECRET environment variable is required') })();
  const payload: JwtPayload = {
    id: user.externalId,
    email: user.email,
    name: user.name,
  };

  return jwt.sign(payload, secret, { expiresIn: "7d" });
}

export function createJwtDecoder() {
  const secret = process.env.JWT_SECRET || (() => { throw new Error('JWT_SECRET environment variable is required') })();

  return (token: string): JwtPayload => {
    return jwt.verify(token, secret) as JwtPayload;
  };
}

export function verifyJwtToken(token: string): JwtPayload | null {
  try {
    const secret = process.env.JWT_SECRET || (() => { throw new Error('JWT_SECRET environment variable is required') })();
    return jwt.verify(token, secret) as JwtPayload;
  } catch (error) {
    return null;
  }
}
