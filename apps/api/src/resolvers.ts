import {
  AuthPayload,
  SignUpInput,
  SignInInput,
  Auth<PERSON>rov<PERSON>,
  BoardList,
  CardList,
  User,
} from "@kanban/graphql-schema";
import { UserQueries } from "@kanban/data-access";
import { createJwtToken } from "./auth/jwt";

// Helper function to create User object from database user data
function createUserObject(user: any): User {
  return {
    externalId: user.externalId,
    name: user.name,
    email: user.email,
    authMethods: [], // Will be populated by GraphQL resolver
    assignedCards: {} as CardList,
    ownedBoards: {} as BoardList,
    createdBy: user.createdBy,
    createdOn: user.createdOn,
    deletedBy: user.deletedBy || null,
    deletedOn: user.deletedOn || null,
  };
}

export const resolvers = {
  Mutation: {
    signUp: async (
      _: any,
      { input }: { input: SignUpInput },
      { driver }: any
    ): Promise<AuthPayload> => {
      const userQueries = new UserQueries(driver);

      try {
        // Check if user with this email already exists
        const existingUser = await userQueries.findUserByEmail(input.email);

        let user;

        if (existingUser) {
          // User exists, check if they already have LOCAL auth
          const existingAuth = await userQueries.findUserAuth(
            existingUser.externalId,
            AuthProvider.Local
          );

          if (existingAuth) {
            throw new Error("User with this email and provider already exists");
          }

          // Add LOCAL auth method to existing user
          await userQueries.addLocalAuthToUser(
            existingUser.externalId,
            input.email,
            input.password
          );

          user = existingUser;
        } else {
          // Create new user and LOCAL auth method
          console.log("Creating user with params:", {
            email: input.email,
            name: input.name,
          });

          const userId = await userQueries.createUserWithLocalAuth(
            input.email,
            input.name,
            input.password
          );

          // Re-query to get properly formatted user data
          user = await userQueries.findUserByExternalId(userId);
        }

        const token = createJwtToken({
          externalId: user.externalId,
          email: user.email,
          name: user.name,
        });

        return {
          token,
          user: createUserObject(user),
        };
      } catch (error) {
        throw error;
      }
    },

    signIn: async (
      _: any,
      { input }: { input: SignInInput },
      { driver }: any
    ): Promise<AuthPayload> => {
      const userQueries = new UserQueries(driver);

      try {
        // Find user auth for LOCAL provider
        const result = await userQueries.findUserByEmailAndProvider(
          input.email,
          AuthProvider.Local
        );

        if (!result) {
          throw new Error("Invalid email or password");
        }

        const { user, userAuth } = result;

        // Check password
        const isValidPassword = await userQueries.validatePassword(
          userAuth.password,
          input.password
        );
        if (!isValidPassword) {
          throw new Error("Invalid email or password");
        }

        const token = createJwtToken({
          externalId: user.externalId,
          email: user.email,
          name: user.name,
        });

        return {
          token,
          user: createUserObject(user),
        };
      } catch (error) {
        throw error;
      }
    },

    createOrUpdateGoogleUser: async (
      _: any,
      { email, name }: { email: string; name: string },
      { driver }: any
    ): Promise<AuthPayload> => {
      const userQueries = new UserQueries(driver);

      try {
        // Check if user with this email already exists
        const existingUser = await userQueries.findUserByEmail(email);

        let user;

        if (existingUser) {
          // User exists, check if they already have GOOGLE auth
          const existingAuth = await userQueries.findUserAuth(
            existingUser.externalId,
            AuthProvider.Google
          );

          if (existingAuth) {
            // Update existing Google auth
            await userQueries.updateGoogleUserAuth(
              existingUser.externalId,
              email
            );
          } else {
            // Add GOOGLE auth method to existing user
            await userQueries.addGoogleAuthToUser(
              existingUser.externalId,
              email
            );
          }

          // Update user profile with latest info from Google
          await userQueries.updateUserProfile(existingUser.externalId, name);

          // Re-query to get updated user data
          user = await userQueries.findUserByExternalId(
            existingUser.externalId
          );
        } else {
          // Create new user and GOOGLE auth method
          const userId = await userQueries.createUserWithGoogleAuth(
            email,
            name
          );

          // Re-query to get properly formatted user data
          user = await userQueries.findUserByExternalId(userId);
        }

        const token = createJwtToken({
          externalId: user.externalId,
          email: user.email,
          name: user.name,
        });

        return {
          token,
          user: createUserObject(user),
        };
      } catch (error) {
        throw error;
      }
    },
  },

  Query: {
    hello: () => {
      return "Hello from Kanban API!";
    },
    me: async (_: any, __: any, { driver, user }: any) => {
      if (!user) {
        return null;
      }

      const userQueries = new UserQueries(driver);

      try {
        const userData = await userQueries.findUserByExternalId(user.id);

        if (!userData) {
          return null;
        }

        return {
          externalId: userData.externalId,
          name: userData.name,
          email: userData.email,
          authMethods: [], // Will be populated by GraphQL resolver
          createdBy: userData.createdBy,
          createdOn: userData.createdOn,
          deletedBy: userData.deletedBy || null,
          deletedOn: userData.deletedOn || null,
        };
      } catch (error) {
        throw error;
      }
    },
  },
};
