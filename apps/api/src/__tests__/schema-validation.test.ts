import { User, User<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, BoardList } from "@kanban/graphql-schema";
import { resolvers } from "../resolvers";

describe("Schema Validation", () => {
  // Extract valid property names from TypeScript types
  const getValidUserProperties = (): string[] => {
    // Create a mock User object to extract its keys
    const mockUser: User = {
      externalId: "",
      email: "",
      name: "",
      authMethods: [],
      assignedCards: {} as CardList,
      ownedBoards: {} as BoardList,
      createdOn: "",
      createdBy: "",
      deletedOn: null,
      deletedBy: null,
    };
    return Object.keys(mockUser);
  };

  const getValidUserAuthProperties = (): string[] => {
    // Create a mock UserAuth object to extract its keys
    const mockUserAuth: UserAuth = {
      externalId: "",
      provider: AuthProvider.Local,
      providerUserId: "",
      email: "",
      password: null,
      user: {} as User,
      createdOn: "",
      createdBy: "",
      deletedOn: null,
      deletedBy: null,
    };
    return Object.keys(mockUserAuth);
  };

  describe("Cypher queries match GraphQL schema", () => {
    it("should use correct User properties in Cypher queries", () => {
      // Get the actual resolver functions
      const { signUp, createOrUpdateGoogleUser } = resolvers.Mutation;
      const { me } = resolvers.Query;

      // Convert resolver functions to strings to analyze their Cypher queries
      const signUpCode = signUp.toString();
      const createOrUpdateGoogleUserCode = createOrUpdateGoogleUser.toString();
      const meCode = me.toString();

      const validUserProperties = getValidUserProperties();

      // Common invalid properties that developers might accidentally use
      const commonInvalidProperties = [
        "id", // Should be 'externalId'
        "createdAt", // Should be 'createdOn'
        "updatedAt", // Doesn't exist in GraphQL schema
        "provider", // Moved to UserAuth
      ];

      // Extract properties used in User CREATE statements and verify they're all valid
      const userCreateMatches = [
        ...signUpCode.matchAll(/CREATE \(u:User \{([^}]+)\}/g),
        ...createOrUpdateGoogleUserCode.matchAll(
          /CREATE \(u:User \{([^}]+)\}/g
        ),
      ];

      userCreateMatches.forEach((match) => {
        const propertiesString = match[1];
        // Extract property names (everything before ':')
        const propertyNames = propertiesString
          .split(",")
          .map((prop) => prop.trim().split(":")[0].trim())
          .filter((prop) => prop.length > 0);

        propertyNames.forEach((propName) => {
          expect(validUserProperties).toContain(propName);
        });
      });
    });

    it("should use correct UserAuth properties in Cypher queries", () => {
      const { signUp, createOrUpdateGoogleUser } = resolvers.Mutation;

      const signUpCode = signUp.toString();
      const createOrUpdateGoogleUserCode = createOrUpdateGoogleUser.toString();

      const validUserAuthProperties = getValidUserAuthProperties();

      // Extract properties used in UserAuth CREATE statements and verify they're all valid
      const userAuthCreateMatches = [
        ...signUpCode.matchAll(/CREATE \(ua:UserAuth \{([^}]+)\}/g),
        ...createOrUpdateGoogleUserCode.matchAll(
          /CREATE \(ua:UserAuth \{([^}]+)\}/g
        ),
      ];

      userAuthCreateMatches.forEach((match) => {
        const propertiesString = match[1];
        // Extract property names (everything before ':')
        const propertyNames = propertiesString
          .split(",")
          .map((prop) => prop.trim().split(":")[0].trim())
          .filter((prop) => prop.length > 0);

        propertyNames.forEach((propName) => {
          expect(validUserAuthProperties).toContain(propName);
        });
      });
    });

    it("should return User objects with correct property names", () => {
      const validUserProperties = getValidUserProperties();

      // Mock database user object (what comes from Cypher)
      const mockDbUser = {
        externalId: "user-123",
        email: "<EMAIL>",
        name: "Test User",
        createdOn: "2024-01-01T00:00:00Z",
        createdBy: "user-123",
      };

      // Test that resolver return objects match GraphQL User type
      const mockUserResponse = {
        externalId: mockDbUser.externalId,
        name: mockDbUser.name,
        email: mockDbUser.email,
        authMethods: [],
        assignedCards: {} as CardList,
        ownedBoards: {} as BoardList,
        createdBy: mockDbUser.createdBy,
        createdOn: mockDbUser.createdOn,
        deletedBy: null,
        deletedOn: null,
      };

      // Verify all properties in the response are valid User properties
      Object.keys(mockUserResponse).forEach((propName) => {
        expect(validUserProperties).toContain(propName);
      });

      // Verify all required User fields are present (non-nullable fields from schema)
      const requiredUserFields = [
        "externalId",
        "email",
        "name",
        "authMethods",
        "createdOn",
        "createdBy",
      ];
      requiredUserFields.forEach((field) => {
        expect(mockUserResponse).toHaveProperty(field);
        expect(
          mockUserResponse[field as keyof typeof mockUserResponse]
        ).not.toBeNull();
        expect(
          mockUserResponse[field as keyof typeof mockUserResponse]
        ).not.toBeUndefined();
      });

      // Verify the response object could be assigned to User type (structural compatibility)
      const userTypeCheck: User = mockUserResponse;
      expect(userTypeCheck).toBeDefined();
    });
  });
});
