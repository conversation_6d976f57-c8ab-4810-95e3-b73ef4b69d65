{"name": "@kanban/api", "version": "1.0.0", "private": true, "scripts": {"build": "tsc", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "lint": "eslint src --ext .ts", "clean": "rm -rf dist", "test": "jest"}, "dependencies": {"@apollo/server": "^5.0.0", "@as-integrations/express5": "^1.1.2", "@graphql-tools/merge": "^9.1.1", "@graphql-tools/schema": "^10.0.25", "@kanban/graphql-schema": "workspace:*", "@kanban/data-access": "workspace:*", "@neo4j/graphql": "^7.2.5", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "neo4j-driver": "^5.28.1"}, "devDependencies": {"@kanban/tsconfig": "workspace:*", "@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "jest": "^30.0.5", "ts-jest": "^29.4.0", "ts-node-dev": "^2.0.0"}}