import { useState } from "react";
import { signIn, getSession } from "next-auth/react";
import { useSignUpMutation, useSignInMutation } from "@kanban/graphql-schema/dist/client";
import { 
  <PERSON><PERSON><PERSON>ider, 
  Container, 
  Card, 
  Button, 
  TextField, 
  Typography, 
  Box, 
  Stack,
  Alert 
} from "@kanban/design-system";

export default function AuthHome() {
  const [isSignUp, setIsSignUp] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    name: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const [signUpMutation] = useSignUpMutation();
  const [signInMutation] = useSignInMutation();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      let token: string | undefined;

      if (isSignUp) {
        const result = await signUpMutation({
          variables: {
            input: {
              email: formData.email,
              password: formData.password,
              name: formData.name,
            },
          },
        });
        token = result.data?.signUp?.token;
      } else {
        const result = await signInMutation({
          variables: {
            input: {
              email: formData.email,
              password: formData.password,
            },
          },
        });
        token = result.data?.signIn?.token;
      }

      if (token) {
        // Store token in localStorage and redirect
        localStorage.setItem("authToken", token);
        const redirectUrl =
          process.env.NEXT_PUBLIC_UI_URL || (() => { throw new Error('NEXT_PUBLIC_UI_URL environment variable is required') })();
        window.location.href = `${redirectUrl}/home?token=${token}`;
      }
    } catch (err: any) {
      setError(err.message || "Authentication failed");
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      await signIn("google", {
        callbackUrl: `${
          process.env.NEXT_PUBLIC_UI_URL || (() => { throw new Error('NEXT_PUBLIC_UI_URL environment variable is required') })()
        }/home`,
      });
    } catch (err) {
      setError("Google sign-in failed");
      setIsLoading(false);
    }
  };

  return (
    <ThemeProvider>
      <Container centered>
        <Card sx={{ width: '100%', maxWidth: 400 }}>
          <Stack spacing={3}>
            <Typography variant="h1" textAlign="center">
              {isSignUp ? "Sign Up" : "Sign In"} to Kanban
            </Typography>

            {error && (
              <Alert severity="error">
                {error}
              </Alert>
            )}

            <Box component="form" onSubmit={handleSubmit}>
              <Stack spacing={2}>
                {isSignUp && (
                  <TextField
                    type="text"
                    name="name"
                    label="Name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                )}

                <TextField
                  type="email"
                  name="email"
                  label="Email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />

                <TextField
                  type="password"
                  name="password"
                  label="Password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                />

                <Button
                  type="submit"
                  variant="primary"
                  loading={isLoading}
                  disabled={isLoading}
                  fullWidth
                >
                  {isSignUp ? "Sign Up" : "Sign In"}
                </Button>
              </Stack>
            </Box>

            <Typography variant="body2" textAlign="center" color="text.secondary">
              Or
            </Typography>

            <Button
              onClick={handleGoogleSignIn}
              disabled={isLoading}
              loading={isLoading}
              fullWidth
              sx={{ backgroundColor: '#db4437', '&:hover': { backgroundColor: '#c23321' } }}
            >
              Sign in with Google
            </Button>

            <Typography variant="body2" textAlign="center" color="text.secondary">
              {isSignUp ? "Already have an account?" : "Don't have an account?"}{" "}
              <Button
                variant="ghost"
                onClick={() => setIsSignUp(!isSignUp)}
                sx={{ p: 0, minWidth: 'auto', textDecoration: 'underline' }}
              >
                {isSignUp ? "Sign In" : "Sign Up"}
              </Button>
            </Typography>
          </Stack>
        </Card>
      </Container>
    </ThemeProvider>
  );
}
