import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import { apolloClient } from "../../../lib/apollo-client";
import { CreateOrUpdateGoogleUserDocument } from "@kanban/graphql-schema/dist/client";

export default NextAuth({
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === "google") {
        try {
          // Create or update user in Neo4j via GraphQL
          const result = await apolloClient.mutate({
            mutation: CreateOrUpdateGoogleUserDocument,
            variables: {
              email: user.email,
              name: user.name,
            },
          });

          const authData = result.data?.createOrUpdateGoogleUser;
          if (authData?.token) {
            // Store token in user object to pass to JWT callback
            (user as any).token = authData.token;
            (user as any).dbUser = authData.user;
            return true;
          }
        } catch (error) {
          console.error("Error creating/updating Google user:", error);
          return false;
        }
      }
      return true;
    },

    async jwt({ token, user }) {
      // Persist the OAuth access_token and user data to the token right after signin
      if (user) {
        token.accessToken = (user as any).token;
        token.dbUser = (user as any).dbUser;
      }
      return token;
    },

    async session({ session, token }) {
      // Send properties to the client
      (session as any).accessToken = token.accessToken;
      session.user = token.dbUser || session.user;
      return session;
    },

    async redirect({ url }) {
      // Redirect to success page which will handle token transfer to UI
      return "/success";
    },
  },
  pages: {
    signIn: "/", // Use our custom sign-in page
  },
  secret: process.env.NEXTAUTH_SECRET,
});
