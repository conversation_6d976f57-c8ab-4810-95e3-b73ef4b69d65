import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';

export default function SuccessPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (status === 'authenticated' && session) {
      // Get the JWT token from the session
      const token = (session as any).accessToken;
      const uiUrl = process.env.NEXT_PUBLIC_UI_URL;
      
      console.log('Session:', session);
      console.log('Token:', token);
      
      if (token && uiUrl) {
        // Redirect to UI with token
        const redirectUrl = `${uiUrl}/home?token=${encodeURIComponent(token)}`;
        console.log('Redirecting to:', redirectUrl);
        window.location.href = redirectUrl;
      } else {
        console.error('Missing token or UI URL:', { token: !!token, uiUrl });
        // Fallback redirect without token
        window.location.href = `${uiUrl}/home`;
      }
    } else if (status === 'unauthenticated') {
      // Not authenticated, redirect to sign in
      router.push('/');
    }
  }, [session, status, router]);

  if (status === 'loading') {
    return <div>Authenticating...</div>;
  }

  if (status === 'unauthenticated') {
    return <div>Authentication failed. Redirecting...</div>;
  }

  return <div>Authentication successful. Redirecting to dashboard...</div>;
}