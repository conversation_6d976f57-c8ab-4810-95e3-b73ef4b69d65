Next let's build out the docker compose file.  
There should be 4 containers: 1 for a Neo4J Database with a persistent volume, and the other 3 for the apps.  
All of the docker files should take in needed parameters from the environment.  
Define a general .env file but also a .env.local and a .env.production, both of which should be git ignored.  
Make sure you can run docker compose up successfully.
