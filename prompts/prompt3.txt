Let's get the authentication flow working correctly.  
In the Auth application the user should be able to login either through Forms Authentication against Users stored as a User node in the Neo4J database container, or using OIDC from Google.  
The User node will need to be used by both the UI application as well as the auth application, so it should be in the shared graphql-schema package.  
The communication with neo4j should happen with the neo4-graphql library rather than explicit cypher calls.
Once the user has authenticated, they should be redirected to a separate /home screen in the application which shows the list kanban boards they are associated with.  
That page should show the currently logged in user as well as a logout button, which would log the user out and redirect then back to the unauthenticated root page.  
Let me know if you have any questions on the implementation.