Create a mono-repo using TurboRepo that manages a kanban application.  
There should be applications for the Kanban UI Web App, the Kanban API Web API, and the OIDC Authentication Web App.  
All apps should be written in TypeScript and should share a tsconfig package.  
The UI Web App written in React talks to the API Web App using Apollo GraphQL.  
The UI redirects to the Auth app also written in React when the user clicks Login.  
There should be a shared package for the GraphQL schema files, as well as the generated models (TypeScript types generated from those GraphQL files).  
Don't worry about generating the specific APIs or UIs right now, each project should just have a single simple root page.  
Just set up the projects correctly, using pnpm and TurboRepo.  
Make sure you can install, build and run all projects successfully.