using System.Text;
using Kanban.Integrations.Ado;
using ReverseMarkdown;

namespace Kanban.Integrations.Services;

public static class CypherService
{
    private static readonly Converter MarkdownConverter = new();

    public static string TransformToCypher(string boardUrl, string boardTitle, WorkItemModel[] workItems)
    {
        var uctNow = DateTime.UtcNow.ToString("O");
        var defaultUser = "11111111-1111-1111-1111-111111111111";
        var cypher = new StringBuilder();
        var columnIds = workItems.Select(w => w.Fields.State)
            .Distinct()
            .ToDictionary(state => state, _ => Guid.NewGuid());

        // Collect unique users from all work items
        var uniqueUsers = new Dictionary<string, User>();
        foreach (var workItem in workItems)
        {
            if (!string.IsNullOrEmpty(workItem.Fields.CreatedBy.Email))
                uniqueUsers[workItem.Fields.CreatedBy.Email] = workItem.Fields.CreatedBy;
            if (!string.IsNullOrEmpty(workItem.Fields.ChangedBy.Email))
                uniqueUsers[workItem.Fields.ChangedBy.Email] = workItem.Fields.ChangedBy;
        }

        // Generate MERGE statements for User nodes
        foreach (var user in uniqueUsers.Values)
        {
            cypher.AppendLine($"MERGE (u:User {{ email: '{user.Email}' }})");
            cypher.AppendLine(
                $"ON CREATE SET u.externalId = randomUUID(), u.displayName = '{user.UserName}', u.createdOn = '{uctNow}'");
            cypher.AppendLine($"ON MATCH SET u.displayName = '{user.UserName}'");
            cypher.AppendLine(";");
        }

        foreach (var workItem in workItems)
        {
            cypher.AppendLine($"MERGE (i:Import {{ url: '{boardUrl}' }})");
            cypher.AppendLine("ON CREATE SET i.externalId = randomUUID()");
            cypher.AppendLine($"MERGE (i)<-[:IMPORTED_FROM]-(b:Board {{ name: '{boardTitle}' }})");
            cypher.AppendLine(
                $"ON CREATE SET b.externalId = randomUUID(), b.createdOn = '{uctNow}', b.createdBy = '{defaultUser}', b.columnOrder = [{string.Join(", ", columnIds.Values.Select(v => $"'{v}'"))}]");
            cypher.AppendLine($"MERGE (b)<-[:IS_STATE_IN]-(col:Column {{ name: '{workItem.Fields.State}' }})");
            cypher.AppendLine(
                $"ON CREATE SET col.externalId = '{columnIds[workItem.Fields.State]}', col.createdOn = '{uctNow}', col.modifiedOn = '{uctNow}', col.cardPriorityOrder = [] ");
            cypher.AppendLine($"MERGE (col)<-[:HAS_STATE]-(c:Card {{ importedId: '{workItem.Id}' }} )");
            cypher.AppendLine("ON CREATE SET");
            cypher.AppendLine("  c.externalId = randomUUID(),");
            OutputSetClause(cypher, workItem);
            cypher.AppendLine("ON MATCH SET");
            OutputSetClause(cypher, workItem);

            // Create relationships to User nodes
            if (!string.IsNullOrEmpty(workItem.Fields.CreatedBy.Email))
            {
                cypher.AppendLine($"WITH c");
                cypher.AppendLine($"MATCH (createdByUser:User {{ email: '{workItem.Fields.CreatedBy.Email}' }})");
                cypher.AppendLine($"MERGE (c)-[:CREATED_BY]->(createdByUser)");
            }

            if (!string.IsNullOrEmpty(workItem.Fields.ChangedBy.Email))
            {
                cypher.AppendLine($"WITH c");
                cypher.AppendLine($"MATCH (modifiedByUser:User {{ email: '{workItem.Fields.ChangedBy.Email}' }})");
                cypher.AppendLine($"MERGE (c)-[:MODIFIED_BY]->(modifiedByUser)");
            }

            cypher.AppendLine(";");
        }

        cypher.AppendLine("MATCH (c:Column) where c.type is null and c.name = 'To Do' set c.type = 'NOT_STARTED';");
        cypher.AppendLine("MATCH (c:Column) where c.type is null and c.name = 'Doing' set c.type = 'IN_PROGRESS';");
        cypher.AppendLine("MATCH (c:Column) where c.type is null and c.name = 'Done' set c.type = 'COMPLETED';");
        return cypher.ToString();
    }

    private static void OutputSetClause(StringBuilder cypher, WorkItemModel workItem)
    {
        var markdownDescription = ConvertHtmlToMarkdown(workItem.Fields.Description);

        cypher.AppendLine($"  c.title= '{workItem.Fields.Title}',");
        cypher.AppendLine($"  c.description= '{markdownDescription}',");
        cypher.AppendLine($"  c.createdOn= '{workItem.Fields.CreatedDate.ToString("O")}',");
        cypher.AppendLine($"  c.modifiedOn= '{workItem.Fields.ChangedDate}',");
        cypher.AppendLine($"  c.tags= []");
    }

    private static string ConvertHtmlToMarkdown(string htmlContent)
    {
        if (string.IsNullOrEmpty(htmlContent))
            return string.Empty;
        try
        {
            return MarkdownConverter.Convert(htmlContent);
        }
        catch (Exception)
        {
            // If conversion fails, return the original HTML content
            return htmlContent;
        }
    }
}