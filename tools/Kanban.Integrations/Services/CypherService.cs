using System.Text;
using Kanban.Integrations.Ado;
using ReverseMarkdown;

namespace Kanban.Integrations.Services;

public static class CypherService
{
    private static readonly Converter MarkdownConverter = new();

    public static string TransformToCypher(string boardUrl, string boardTitle, WorkItemModel[] workItems, Dictionary<int, WorkItemComment[]>? workItemComments = null)
    {
        var uctNow = DateTime.UtcNow.ToString("O");
        var defaultUser = "11111111-1111-1111-1111-111111111111";
        var cypher = new StringBuilder();
        var columnIds = workItems.Select(w => w.Fields.State)
            .Distinct()
            .ToDictionary(state => state, _ => Guid.NewGuid());

        // Collect unique users from all work items
        var uniqueUsers = new Dictionary<string, User>();
        foreach (var workItem in workItems)
        {
            if (!string.IsNullOrEmpty(workItem.Fields.CreatedBy.Email))
                uniqueUsers[workItem.Fields.CreatedBy.Email] = workItem.Fields.CreatedBy;
            if (!string.IsNullOrEmpty(workItem.Fields.ChangedBy.Email))
                uniqueUsers[workItem.Fields.ChangedBy.Email] = workItem.Fields.ChangedBy;
        }

        // Collect unique users from comments
        if (workItemComments != null)
        {
            foreach (var commentArray in workItemComments.Values)
            {
                foreach (var comment in commentArray)
                {
                    if (!string.IsNullOrEmpty(comment.CreatedBy?.Email))
                        uniqueUsers[comment.CreatedBy.Email] = comment.CreatedBy;
                    if (!string.IsNullOrEmpty(comment.ModifiedBy?.Email))
                        uniqueUsers[comment.ModifiedBy.Email] = comment.ModifiedBy;
                }
            }
        }

        // Generate MERGE statements for User nodes
        foreach (var user in uniqueUsers.Values)
        {
            cypher.AppendLine($"MERGE (u:User {{ email: '{user.Email}' }})");
            cypher.AppendLine(
                $"ON CREATE SET u.externalId = randomUUID(), u.displayName = '{user.UserName}', u.createdOn = '{uctNow}'");
            cypher.AppendLine($"ON MATCH SET u.displayName = '{user.UserName}'");
            cypher.AppendLine(";");
        }

        foreach (var workItem in workItems)
        {
            cypher.AppendLine($"MERGE (i:Import {{ url: '{boardUrl}' }})");
            cypher.AppendLine("ON CREATE SET i.externalId = randomUUID()");
            cypher.AppendLine($"MERGE (i)<-[:IMPORTED_FROM]-(b:Board {{ name: '{boardTitle}' }})");
            cypher.AppendLine(
                $"ON CREATE SET b.externalId = randomUUID(), b.createdOn = '{uctNow}', b.createdBy = '{defaultUser}', b.columnOrder = [{string.Join(", ", columnIds.Values.Select(v => $"'{v}'"))}]");
            cypher.AppendLine($"MERGE (b)<-[:IS_STATE_IN]-(col:Column {{ name: '{workItem.Fields.State}' }})");
            cypher.AppendLine(
                $"ON CREATE SET col.externalId = '{columnIds[workItem.Fields.State]}', col.createdOn = '{uctNow}', col.modifiedOn = '{uctNow}', col.cardPriorityOrder = [] ");
            cypher.AppendLine($"MERGE (col)<-[:HAS_STATE]-(c:Card {{ importedId: '{workItem.Id}' }} )");
            cypher.AppendLine("ON CREATE SET");
            cypher.AppendLine("  c.externalId = randomUUID(),");
            OutputSetClause(cypher, workItem);
            cypher.AppendLine("ON MATCH SET");
            OutputSetClause(cypher, workItem);

            // Create relationships to User nodes
            if (!string.IsNullOrEmpty(workItem.Fields.CreatedBy.Email))
            {
                cypher.AppendLine($"WITH c");
                cypher.AppendLine($"MATCH (createdByUser:User {{ email: '{workItem.Fields.CreatedBy.Email}' }})");
                cypher.AppendLine($"MERGE (c)-[:CREATED_BY]->(createdByUser)");
            }

            if (!string.IsNullOrEmpty(workItem.Fields.ChangedBy.Email))
            {
                cypher.AppendLine($"WITH c");
                cypher.AppendLine($"MATCH (modifiedByUser:User {{ email: '{workItem.Fields.ChangedBy.Email}' }})");
                cypher.AppendLine($"MERGE (c)-[:MODIFIED_BY]->(modifiedByUser)");
            }

            cypher.AppendLine(";");
        }

        // Create comments for work items
        if (workItemComments != null)
        {
            foreach (var workItem in workItems)
            {
                if (workItemComments.TryGetValue(workItem.Id, out var comments) && comments.Length > 0)
                {
                    // Create CommentList for this card and all comments in one transaction
                    cypher.AppendLine($"MATCH (c:Card {{ importedId: '{workItem.Id}' }})");
                    cypher.AppendLine($"MERGE (c)-[:HAS_COMMENT_LIST]->(cl:CommentList)");
                    cypher.AppendLine($"ON CREATE SET cl.externalId = randomUUID()");

                    // Create individual comments
                    foreach (var comment in comments)
                    {
                        var commentContent = EscapeForCypher(ConvertHtmlToMarkdown(comment.Text));
                        var commentId = Guid.NewGuid().ToString();

                        cypher.AppendLine($"WITH c, cl");
                        cypher.AppendLine($"CREATE (comment:Comment {{");
                        cypher.AppendLine($"  externalId: '{commentId}',");
                        cypher.AppendLine($"  content: '{commentContent}',");
                        cypher.AppendLine($"  createdOn: '{comment.CreatedDate:O}',");
                        cypher.AppendLine($"  createdBy: '{GetUserIdForEmail(comment.CreatedBy?.Email)}',");

                        if (comment.ModifiedBy != null && comment.ModifiedDate != comment.CreatedDate)
                        {
                            cypher.AppendLine($"  modifiedOn: '{comment.ModifiedDate:O}',");
                            cypher.AppendLine($"  modifiedBy: '{GetUserIdForEmail(comment.ModifiedBy.Email)}'");
                        }
                        else
                        {
                            cypher.AppendLine($"  modifiedOn: null,");
                            cypher.AppendLine($"  modifiedBy: null");
                        }

                        cypher.AppendLine($"}})");

                        // Create relationship from CommentList to Comment
                        cypher.AppendLine($"WITH c, cl, comment");
                        cypher.AppendLine($"MERGE (cl)<-[:IN]-(comment)");

                        // Create nested CommentList for the comment (as per schema)
                        cypher.AppendLine($"WITH c, cl, comment");
                        cypher.AppendLine($"MERGE (comment)-[:HAS_COMMENT_LIST]->(nestedCl:CommentList)");
                        cypher.AppendLine($"ON CREATE SET nestedCl.externalId = randomUUID()");
                    }

                    cypher.AppendLine(";");
                }
            }
        }

        cypher.AppendLine("MATCH (c:Column) where c.type is null and c.name = 'To Do' set c.type = 'NOT_STARTED';");
        cypher.AppendLine("MATCH (c:Column) where c.type is null and c.name = 'Doing' set c.type = 'IN_PROGRESS';");
        cypher.AppendLine("MATCH (c:Column) where c.type is null and c.name = 'Done' set c.type = 'COMPLETED';");
        return cypher.ToString();
    }

    private static void OutputSetClause(StringBuilder cypher, WorkItemModel workItem)
    {
        var markdownDescription = ConvertHtmlToMarkdown(workItem.Fields.Description);

        cypher.AppendLine($"  c.title= '{workItem.Fields.Title}',");
        cypher.AppendLine($"  c.description= '{markdownDescription}',");
        cypher.AppendLine($"  c.createdOn= '{workItem.Fields.CreatedDate.ToString("O")}',");
        cypher.AppendLine($"  c.modifiedOn= '{workItem.Fields.ChangedDate}',");
        cypher.AppendLine($"  c.tags= []");
    }

    private static string ConvertHtmlToMarkdown(string htmlContent)
    {
        if (string.IsNullOrEmpty(htmlContent))
            return string.Empty;
        try
        {
            return MarkdownConverter.Convert(htmlContent);
        }
        catch (Exception)
        {
            // If conversion fails, return the original HTML content
            return htmlContent;
        }
    }

    private static string EscapeForCypher(string input)
    {
        if (string.IsNullOrEmpty(input))
            return string.Empty;

        return input
            .Replace("\\", "\\\\")  // Escape backslashes first
            .Replace("'", "\\'")    // Escape single quotes
            .Replace("\"", "\\\"")  // Escape double quotes
            .Replace("\n", "\\n")   // Escape newlines
            .Replace("\r", "\\r")   // Escape carriage returns
            .Replace("\t", "\\t");  // Escape tabs
    }

    private static string GetUserIdForEmail(string? email)
    {
        if (string.IsNullOrEmpty(email))
            return "11111111-1111-1111-1111-111111111111"; // Default user ID

        // For now, we'll use a deterministic UUID based on email
        // In a real implementation, you might want to look up actual user IDs
        var emailBytes = System.Text.Encoding.UTF8.GetBytes(email);
        var hash = System.Security.Cryptography.MD5.HashData(emailBytes);
        var guid = new Guid(hash);
        return guid.ToString();
    }
}