using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Kanban.Integrations.Ado;

namespace Kanban.Integrations.Services;

public static class AzureDevops
{
  public static async Task<WorkItemModel[]> ExportWorkItems(string organizationUrl, string projectName,
    string personalAccessToken)
  {
    using HttpClient client = new HttpClient();

    var authToken = Convert.ToBase64String(Encoding.ASCII.GetBytes($":{personalAccessToken}"));
    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authToken);

    // Ensure ado directory exists
    await EnsureAdoDirectoryExists();

// Project details
    string projectUrl = $"{organizationUrl}/_apis/projects/{projectName}?api-version=7.0-preview";
    Console.WriteLine("Retrieving project details...");
    await GetAndDisplayDataAsync(client, projectUrl, "project-details");

//  Work Items
    Console.WriteLine("\nRetrieving all work items...");
    var result = await GetWorkItemsAsync(client, organizationUrl, projectName);

    // Fetch comments for all work items
    Console.WriteLine("\nRetrieving comments for all work items...");
    await GetWorkItemCommentsAsync(client, organizationUrl, projectName, result);

    Console.WriteLine("\nData extraction complete.");

    return result;
  }

  public static async Task<(WorkItemModel[] workItems, BoardColumn[] boardColumns, Dictionary<int, WorkItemComment[]> comments)> ExportWorkItemsWithComments(
    string organizationUrl, string projectName, string personalAccessToken)
  {
    using HttpClient client = new HttpClient();

    var authToken = Convert.ToBase64String(Encoding.ASCII.GetBytes($":{personalAccessToken}"));
    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authToken);

    // Ensure ado directory exists
    await EnsureAdoDirectoryExists();

    // Project details
    string projectUrl = $"{organizationUrl}/_apis/projects/{projectName}?api-version=7.0-preview";
    Console.WriteLine("Retrieving project details...");
    await GetAndDisplayDataAsync(client, projectUrl, "project-details");

    // Get board columns
    Console.WriteLine("\nRetrieving board columns...");
    var boardColumns = await GetBoardColumnsAsync(client, organizationUrl, projectName);

    // Work Items
    Console.WriteLine("\nRetrieving all work items...");
    var workItems = await GetWorkItemsAsync(client, organizationUrl, projectName);

    // Fetch comments for all work items with structured data
    Console.WriteLine("\nRetrieving comments for all work items...");
    var comments = await GetWorkItemCommentsWithDataAsync(client, organizationUrl, projectName, workItems);

    Console.WriteLine("\nData extraction complete.");

    return (workItems, boardColumns, comments);
  }

  static async Task GetAndDisplayDataAsync(HttpClient client, string url, string filePrefix = "response")
  {
    try
    {
      HttpResponseMessage response = await client.GetAsync(url);

      if (response.IsSuccessStatusCode)
      {
        // Read and display the response content (JSON)
        string content = await response.Content.ReadAsStringAsync();
        Console.WriteLine("Response from " + url + ":");
        Console.WriteLine(content);
        
        // Save formatted JSON to file
        await SaveJsonToFile(content, filePrefix);
      }
      else
      {
        Console.WriteLine($"Error retrieving data from {url}. Status Code: {response.StatusCode}");
        string errorContent = await response.Content.ReadAsStringAsync();
        Console.WriteLine("Error details: " + errorContent);
        
        // Save error response to file
        await SaveJsonToFile(errorContent, $"{filePrefix}-error");
      }
    }
    catch (Exception ex)
    {
      Console.WriteLine($"Exception when calling {url}: {ex.Message}");
    }
  }

  static async Task<WorkItemModel[]> GetWorkItemsAsync(HttpClient client, string organizationUrl, string projectName)
  {
    //WIQL
    var query = new
    {
      query =
        $"SELECT [System.Id], [System.AssignedTo], [System.State], [System.Title], [System.Tags] FROM WorkItems WHERE [System.TeamProject] = '{projectName}' ORDER BY [System.ChangedDate] DESC"
    };

    string queryJson = JsonSerializer.Serialize(query);
    // WIQL endpoint for the project
    string wiqlUrl = $"{organizationUrl}/{projectName}/_apis/wit/wiql?api-version=7.0-preview";
    using var content = new StringContent(queryJson, Encoding.UTF8, "application/json");

    Console.WriteLine("Executing WIQL query to retrieve work item IDs...");
    HttpResponseMessage wiqlResponse = await client.PostAsync(wiqlUrl, content);

    if (!wiqlResponse.IsSuccessStatusCode)
    {
      Console.WriteLine($"Failed to run WIQL query. Status code: {wiqlResponse.StatusCode}");
      string error = await wiqlResponse.Content.ReadAsStringAsync();
      Console.WriteLine("Error: " + error);
      return [];
    }

    string wiqlResult = await wiqlResponse.Content.ReadAsStringAsync();
    
    // Save WIQL query response to file
    await SaveJsonToFile(wiqlResult, "wiql-query");

    var workItemIds = new List<int>();

    try
    {
      using JsonDocument doc = JsonDocument.Parse(wiqlResult);
      JsonElement root = doc.RootElement;
      if (root.TryGetProperty("workItems", out JsonElement workItemsElement))
      {
        foreach (JsonElement item in workItemsElement.EnumerateArray())
        {
          if (item.TryGetProperty("id", out JsonElement idElement) && idElement.TryGetInt32(out int id))
          {
            workItemIds.Add(id);
          }
        }
      }
    }
    catch (Exception ex)
    {
      Console.WriteLine("Error parsing WIQL response: " + ex.Message);
      return [];
    }

    if (workItemIds.Count == 0)
    {
      Console.WriteLine("No work items found for the project.");
      return [];
    }

    Console.WriteLine($"Found {workItemIds.Count} work items. Retrieving details...");

    // Azure DevOps REST API may limit the number of work items returned in a single call.
    // For simplicity, this example assumes all work items can be retrieved in one call.
    // For larger datasets, consider batching the requests.
    string idsString = string.Join(",", workItemIds);
    string workItemsUrl = $"{organizationUrl}/{projectName}/_apis/wit/workitems?ids={idsString}&api-version=7.0-preview";

    HttpResponseMessage workItemsResponse = await client.GetAsync(workItemsUrl);

    if (!workItemsResponse.IsSuccessStatusCode)
    {
      Console.WriteLine($"Failed to retrieve work item details. Status code: {workItemsResponse.StatusCode}");
      string error = await workItemsResponse.Content.ReadAsStringAsync();
      Console.WriteLine("Error: " + error);
      return [];
    }

    string workItemsResult = await workItemsResponse.Content.ReadAsStringAsync();
    
    // Save work items response to file
    await SaveJsonToFile(workItemsResult, "work-items");

    var options = new JsonSerializerOptions
    {
      PropertyNameCaseInsensitive = true,
      AllowTrailingCommas = true,
      ReadCommentHandling = JsonCommentHandling.Skip,
    };
    options.Converters.Add(new TextSanitizerConverter());
    var workItems = JsonSerializer.Deserialize<WorkItemRoot>(workItemsResult, options);
    return workItems.Value;
  }

  static async Task GetWorkItemCommentsAsync(HttpClient client, string organizationUrl, string projectName, WorkItemModel[] workItems)
  {
    if (workItems == null || workItems.Length == 0)
    {
      Console.WriteLine("No work items to fetch comments for.");
      return;
    }

    Console.WriteLine($"Fetching comments for {workItems.Length} work items...");

    foreach (var workItem in workItems)
    {
      try
      {
        Console.WriteLine($"Fetching comments for work item {workItem.Id}...");

        // Azure DevOps REST API endpoint for work item comments
        string commentsUrl = $"{organizationUrl}/{projectName}/_apis/wit/workItems/{workItem.Id}/comments?api-version=7.0-preview";

        HttpResponseMessage commentsResponse = await client.GetAsync(commentsUrl);

        if (commentsResponse.IsSuccessStatusCode)
        {
          string commentsResult = await commentsResponse.Content.ReadAsStringAsync();

          // Save comments response to file
          await SaveJsonToFile(commentsResult, $"work-item-{workItem.Id}-comments");

          // Parse and display comment count
          try
          {
            using JsonDocument doc = JsonDocument.Parse(commentsResult);
            JsonElement root = doc.RootElement;
            if (root.TryGetProperty("count", out JsonElement countElement) && countElement.TryGetInt32(out int commentCount))
            {
              Console.WriteLine($"  Found {commentCount} comments for work item {workItem.Id}");
            }
          }
          catch (Exception ex)
          {
            Console.WriteLine($"  Error parsing comments response for work item {workItem.Id}: {ex.Message}");
          }
        }
        else
        {
          Console.WriteLine($"  Failed to retrieve comments for work item {workItem.Id}. Status code: {commentsResponse.StatusCode}");
          string error = await commentsResponse.Content.ReadAsStringAsync();
          Console.WriteLine($"  Error: {error}");

          // Save error response to file
          await SaveJsonToFile(error, $"work-item-{workItem.Id}-comments-error");
        }
      }
      catch (Exception ex)
      {
        Console.WriteLine($"  Exception when fetching comments for work item {workItem.Id}: {ex.Message}");
      }

      // Add a small delay to avoid overwhelming the API
      await Task.Delay(100);
    }

    Console.WriteLine("Finished fetching comments for all work items.");
  }

  static async Task<Dictionary<int, WorkItemComment[]>> GetWorkItemCommentsWithDataAsync(HttpClient client, string organizationUrl, string projectName, WorkItemModel[] workItems)
  {
    var workItemComments = new Dictionary<int, WorkItemComment[]>();

    if (workItems == null || workItems.Length == 0)
    {
      Console.WriteLine("No work items to fetch comments for.");
      return workItemComments;
    }

    Console.WriteLine($"Fetching and parsing comments for {workItems.Length} work items...");

    var options = new JsonSerializerOptions
    {
      PropertyNameCaseInsensitive = true,
      AllowTrailingCommas = true,
      ReadCommentHandling = JsonCommentHandling.Skip,
    };

    foreach (var workItem in workItems)
    {
      try
      {
        Console.WriteLine($"Fetching comments for work item {workItem.Id}...");

        // Azure DevOps REST API endpoint for work item comments
        string commentsUrl = $"{organizationUrl}/{projectName}/_apis/wit/workItems/{workItem.Id}/comments?api-version=7.0-preview";

        HttpResponseMessage commentsResponse = await client.GetAsync(commentsUrl);

        if (commentsResponse.IsSuccessStatusCode)
        {
          string commentsResult = await commentsResponse.Content.ReadAsStringAsync();

          // Save comments response to file
          await SaveJsonToFile(commentsResult, $"work-item-{workItem.Id}-comments");

          // Parse comments
          try
          {
            var commentsData = JsonSerializer.Deserialize<WorkItemCommentsResponse>(commentsResult, options);
            if (commentsData?.Comments != null)
            {
              workItemComments[workItem.Id] = commentsData.Comments;
              Console.WriteLine($"  Found {commentsData.Comments.Length} comments for work item {workItem.Id}");
            }
            else
            {
              workItemComments[workItem.Id] = Array.Empty<WorkItemComment>();
              Console.WriteLine($"  No comments found for work item {workItem.Id}");
            }
          }
          catch (Exception ex)
          {
            Console.WriteLine($"  Error parsing comments response for work item {workItem.Id}: {ex.Message}");
            workItemComments[workItem.Id] = Array.Empty<WorkItemComment>();
          }
        }
        else
        {
          Console.WriteLine($"  Failed to retrieve comments for work item {workItem.Id}. Status code: {commentsResponse.StatusCode}");
          string error = await commentsResponse.Content.ReadAsStringAsync();
          Console.WriteLine($"  Error: {error}");

          // Save error response to file
          await SaveJsonToFile(error, $"work-item-{workItem.Id}-comments-error");
          workItemComments[workItem.Id] = Array.Empty<WorkItemComment>();
        }
      }
      catch (Exception ex)
      {
        Console.WriteLine($"  Exception when fetching comments for work item {workItem.Id}: {ex.Message}");
        workItemComments[workItem.Id] = Array.Empty<WorkItemComment>();
      }

      // Add a small delay to avoid overwhelming the API
      await Task.Delay(100);
    }

    Console.WriteLine("Finished fetching comments for all work items.");
    return workItemComments;
  }

  static async Task<BoardColumn[]> GetBoardColumnsAsync(HttpClient client, string organizationUrl, string projectName)
  {
    try
    {
      // First, get the list of boards for the project
      Console.WriteLine("Fetching boards for the project...");
      string boardsUrl = $"{organizationUrl}/{projectName}/_apis/work/boards?api-version=7.0";

      HttpResponseMessage boardsResponse = await client.GetAsync(boardsUrl);

      if (!boardsResponse.IsSuccessStatusCode)
      {
        Console.WriteLine($"Failed to retrieve boards. Status code: {boardsResponse.StatusCode}");
        string error = await boardsResponse.Content.ReadAsStringAsync();
        Console.WriteLine($"Error: {error}");
        await SaveJsonToFile(error, "boards-error");
        return [];
      }

      string boardsResult = await boardsResponse.Content.ReadAsStringAsync();
      await SaveJsonToFile(boardsResult, "boards");

      var options = new JsonSerializerOptions
      {
        PropertyNameCaseInsensitive = true,
        AllowTrailingCommas = true,
        ReadCommentHandling = JsonCommentHandling.Skip,
      };

      var boardsData = JsonSerializer.Deserialize<BoardsResponse>(boardsResult, options);

      if (boardsData?.Boards == null || boardsData.Boards.Length == 0)
      {
        Console.WriteLine("No boards found for the project.");
        return [];
      }

      // Use the first board (typically the main Kanban board)
      var mainBoard = boardsData.Boards[0];
      Console.WriteLine($"Using board: {mainBoard.Name} (ID: {mainBoard.Id})");

      // Now get the columns for this board
      Console.WriteLine("Fetching board columns...");
      string columnsUrl = $"{organizationUrl}/{projectName}/_apis/work/boards/{mainBoard.Id}/columns?api-version=7.0";

      HttpResponseMessage columnsResponse = await client.GetAsync(columnsUrl);

      if (!columnsResponse.IsSuccessStatusCode)
      {
        Console.WriteLine($"Failed to retrieve board columns. Status code: {columnsResponse.StatusCode}");
        string error = await columnsResponse.Content.ReadAsStringAsync();
        Console.WriteLine($"Error: {error}");
        await SaveJsonToFile(error, "board-columns-error");
        return [];
      }

      string columnsResult = await columnsResponse.Content.ReadAsStringAsync();
      await SaveJsonToFile(columnsResult, "board-columns");

      var columnsData = JsonSerializer.Deserialize<BoardColumnsResponse>(columnsResult, options);

      if (columnsData?.Columns != null)
      {
        Console.WriteLine($"Found {columnsData.Columns.Length} columns:");
        foreach (var column in columnsData.Columns)
        {
          Console.WriteLine($"  - {column.Name} (ID: {column.Id}, Type: {column.ColumnType})");
          if (column.StateMappings != null)
          {
            foreach (var kvp in column.StateMappings)
            {
              Console.WriteLine($"    WorkItemType: {kvp.Key} -> State: {kvp.Value}");
            }
          }
        }
        return columnsData.Columns;
      }

      Console.WriteLine("No columns found for the board.");
      return [];
    }
    catch (Exception ex)
    {
      Console.WriteLine($"Exception when fetching board columns: {ex.Message}");
      return [];
    }
  }

  private static async Task EnsureAdoDirectoryExists()
  {
    // Get the repository root directory (assuming we're in tools/Kanban.Integrations)
    string currentDirectory = Directory.GetCurrentDirectory();
    string repoRoot = Path.GetFullPath(currentDirectory);
    string adoPath = Path.Combine(repoRoot, "ado");
    
    if (!Directory.Exists(adoPath))
    {
      Directory.CreateDirectory(adoPath);
      Console.WriteLine($"Created ado directory at: {adoPath}");
    }
  }

  private static async Task SaveJsonToFile(string jsonContent, string filePrefix)
  {
    try
    {
      // Get the repository root directory
      string currentDirectory = Directory.GetCurrentDirectory();
      string repoRoot = Path.GetFullPath(currentDirectory);
      string adoPath = Path.Combine(repoRoot, "ado");
      
      // Create timestamp for filename
      string timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
      string fileName = $"{filePrefix}_{timestamp}.json";
      string filePath = Path.Combine(adoPath, fileName);
      
      // Format JSON with proper indentation
      string formattedJson;
      try
      {
        using JsonDocument doc = JsonDocument.Parse(jsonContent);
        var options = new JsonSerializerOptions
        {
          WriteIndented = true
        };
        formattedJson = JsonSerializer.Serialize(doc.RootElement, options);
      }
      catch (JsonException)
      {
        // If JSON parsing fails, save as-is
        formattedJson = jsonContent;
      }
      
      // Save to file
      await File.WriteAllTextAsync(filePath, formattedJson);
      Console.WriteLine($"Saved response to: {filePath}");
    }
    catch (Exception ex)
    {
      Console.WriteLine($"Error saving JSON to file: {ex.Message}");
    }
  }
}
