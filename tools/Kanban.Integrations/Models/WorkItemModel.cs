using System.Text.Json.Serialization;

namespace Kanban.Integrations.Ado;

public class WorkItemModel
{
    public int Id { get; set; }
    public int Rev { get; set; }
    public string Url { get; set; }
    public Fields Fields { get; set; }
}

// [Node("card")]
public class Fields
{
    [JsonPropertyName("System.TeamProject")]
    public string Project { get; set; }
    [JsonPropertyName("System.WorkItemType")]
    public string WorkItemType { get; set; }
    [JsonPropertyName("System.State")]
    public string State { get; set; }
    [JsonPropertyName("System.Description")]
    public string Description { get; set; }
    [JsonPropertyName("System.Reason")]
    public string Reason { get; set; }
    [JsonPropertyName("System.CreatedDate")]
    public DateTime CreatedDate { get; set; }
    [JsonPropertyName("System.CreatedBy")]
    // [Relationship("CreatedBy", RelationshipDirection.Out)]
    public User CreatedBy { get; set; }
    [JsonPropertyName("System.ChangedDate")]
    public DateTime ChangedDate { get; set; }
    [JsonPropertyName("System.ChangedBy")]
    // [Relationship("ChangedBy", RelationshipDirection.Out)]
    public User ChangedBy { get; set; }
    [JsonPropertyName("System.Title")]
    public string Title { get; set; }
    [JsonPropertyName("System.BoardColumn")]
    public string Column { get; set; }
}

public class User
{
    [JsonPropertyName("displayName")]
    public string UserName { get; set; }
    [JsonPropertyName("uniqueName")]
    public string Email { get; set; }
}

public class WorkItemRoot
{
    [JsonPropertyName("count")]
    public int Count { get; set; }
    public WorkItemModel[] Value { get; set; }
}
