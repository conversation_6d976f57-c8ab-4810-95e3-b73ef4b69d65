using System.Text.Json.Serialization;

namespace Kanban.Integrations.Ado;

public class CommentModel
{
    public int Id { get; set; }
    public int WorkItemId { get; set; }
    public int Version { get; set; }
    public string Text { get; set; }
    public User CreatedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public User ModifiedBy { get; set; }
    public DateTime ModifiedDate { get; set; }
    public string Url { get; set; }
}

public class CommentRoot
{
    [JsonPropertyName("count")]
    public int Count { get; set; }
    
    [JsonPropertyName("value")]
    public CommentModel[] Value { get; set; }
}

public class WorkItemComment
{
    public int Id { get; set; }
    public int WorkItemId { get; set; }
    public int Version { get; set; }
    public string Text { get; set; }
    
    [JsonPropertyName("createdBy")]
    public User CreatedBy { get; set; }
    
    [JsonPropertyName("createdDate")]
    public DateTime CreatedDate { get; set; }
    
    [JsonPropertyName("modifiedBy")]
    public User ModifiedBy { get; set; }
    
    [JsonPropertyName("modifiedDate")]
    public DateTime ModifiedDate { get; set; }
    
    public string Url { get; set; }
}

public class WorkItemCommentsResponse
{
    [JsonPropertyName("count")]
    public int Count { get; set; }
    
    [JsonPropertyName("value")]
    public WorkItemComment[] Comments { get; set; }
}
