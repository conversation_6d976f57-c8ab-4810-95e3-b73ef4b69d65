using System.Text.Json.Serialization;

namespace Kanban.Integrations.Ado;

public class BoardColumn
{
    [JsonPropertyName("id")]
    public string Id { get; set; }
    
    [JsonPropertyName("name")]
    public string Name { get; set; }
    
    [JsonPropertyName("itemLimit")]
    public int ItemLimit { get; set; }
    
    [JsonPropertyName("stateMappings")]
    public Dictionary<string, string> StateMappings { get; set; }
    
    [JsonPropertyName("columnType")]
    public string ColumnType { get; set; }
    
    [JsonPropertyName("isSplit")]
    public bool IsSplit { get; set; }
    
    [JsonPropertyName("description")]
    public string Description { get; set; }
}

public class StateMapping
{
    [JsonPropertyName("state")]
    public string State { get; set; }
    
    [JsonPropertyName("metaState")]
    public string MetaState { get; set; }
}

public class BoardColumnsResponse
{
    [JsonPropertyName("count")]
    public int Count { get; set; }
    
    [JsonPropertyName("value")]
    public BoardColumn[] Columns { get; set; }
}

public class Board
{
    [JsonPropertyName("id")]
    public string Id { get; set; }
    
    [JsonPropertyName("name")]
    public string Name { get; set; }
    
    [JsonPropertyName("url")]
    public string Url { get; set; }
}

public class BoardsResponse
{
    [JsonPropertyName("count")]
    public int Count { get; set; }
    
    [JsonPropertyName("value")]
    public Board[] Boards { get; set; }
}
