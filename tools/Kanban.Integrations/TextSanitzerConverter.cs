using System.Text.Json;
using System.Text.Json.Serialization;

namespace Kanban.Integrations;

public class TextSanitizerConverter : JsonConverter<string>
{
  public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
  {
      string original = reader.GetString();
      return original
        .Replace("'", "\\'")
        .Replace("<br>", "\n")
        // .Replace("<div>", "")
        // .Replace(@"</div>", "\n")
        .Replace("&nbsp", " ");
  }

  public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
  {
    return;
  }
}
