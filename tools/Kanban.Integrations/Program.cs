using Kanban.Integrations.Services;
using Microsoft.Extensions.Configuration;

var builder = new ConfigurationBuilder()
  .SetBasePath(Directory.GetCurrentDirectory())
  .AddUserSecrets<Program>();
  // .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

IConfiguration configuration = builder.Build();

string organization = configuration["organization"];
string organizationUrl = $"https://dev.azure.com/{organization}"; // remove trailing slash if any
string projectName = configuration["project"];
string personalAccessToken = configuration["personalAccessToken"];

// Option 1: Export work items only (original functionality)
// var workItems = await AzureDevops.ExportWorkItems(organizationUrl, projectName, personalAccessToken);

// Option 2: Export work items with comments (new functionality)
var (workItems, comments) = await AzureDevops.ExportWorkItemsWithComments(organizationUrl, projectName, personalAccessToken);

Console.WriteLine($"\nSummary:");
Console.WriteLine($"- Retrieved {workItems.Length} work items");
Console.WriteLine($"- Retrieved comments for {comments.Count} work items");

int totalComments = comments.Values.Sum(c => c.Length);
Console.WriteLine($"- Total comments across all work items: {totalComments}");

// Display comment summary per work item
foreach (var workItem in workItems.Take(5)) // Show first 5 work items as example
{
  var commentCount = comments.TryGetValue(workItem.Id, out var workItemComments) ? workItemComments.Length : 0;
  Console.WriteLine($"- Work Item {workItem.Id} ({workItem.Fields.Title}): {commentCount} comments");
}

var cypher = CypherService.TransformToCypher($"{organizationUrl}/{projectName}", projectName, workItems);
Console.WriteLine(cypher);

// Save Cypher to filesystem
await SaveCypherToFile(cypher, projectName);

static async Task SaveCypherToFile(string cypherContent, string projectName)
{
  try
  {
    // Get the repository root directory
    string currentDirectory = Directory.GetCurrentDirectory();
    string repoRoot = Path.GetFullPath(currentDirectory);
    string adoPath = Path.Combine(repoRoot, "ado");

    // Ensure ado directory exists
    if (!Directory.Exists(adoPath))
    {
      Directory.CreateDirectory(adoPath);
      Console.WriteLine($"Created ado directory at: {adoPath}");
    }

    // Create timestamp for filename
    string timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
    string fileName = $"cypher_{projectName}_{timestamp}.cypher";
    string filePath = Path.Combine(adoPath, fileName);

    // Save to file
    await File.WriteAllTextAsync(filePath, cypherContent);
    Console.WriteLine($"\nCypher saved to: {filePath}");
    Console.WriteLine($"File size: {new FileInfo(filePath).Length} bytes");
  }
  catch (Exception ex)
  {
    Console.WriteLine($"Error saving Cypher to file: {ex.Message}");
  }
}