using Kanban.Integrations.Services;
using Microsoft.Extensions.Configuration;

var builder = new ConfigurationBuilder()
  .SetBasePath(Directory.GetCurrentDirectory())
  .AddUserSecrets<Program>();
  // .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

IConfiguration configuration = builder.Build();

string organization = configuration["organization"];
string organizationUrl = $"https://dev.azure.com/{organization}"; // remove trailing slash if any
string projectName = configuration["project"];
string personalAccessToken = configuration["personalAccessToken"];

var workItems = await AzureDevops.ExportWorkItems(organizationUrl, projectName, personalAccessToken);
// var cypher = CypherService.TransformToCypher($"{organizationUrl}/{projectName}", projectName, workItems);
//Console.WriteLine(cypher);