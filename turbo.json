{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "lint": {"dependsOn": ["^lint"]}, "dev": {"cache": false, "persistent": true}, "clean": {"cache": false}, "test": {"dependsOn": ["build"], "outputs": ["cypress/screenshots/**", "cypress/videos/**"]}, "test:e2e": {"dependsOn": ["build"], "outputs": ["cypress/screenshots/**", "cypress/videos/**"]}, "cy:open": {"cache": false, "persistent": true}, "cy:run": {"dependsOn": ["build"], "outputs": ["cypress/screenshots/**", "cypress/videos/**"]}, "cy:run:api": {"dependsOn": ["build"], "outputs": ["cypress/screenshots/**", "cypress/videos/**"]}, "cy:run:ui": {"dependsOn": ["build"], "outputs": ["cypress/screenshots/**", "cypress/videos/**"]}}}