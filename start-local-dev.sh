#!/bin/bash

echo "Starting Kanban Development Environment"
echo "Mode: Neo4j in Docker + Apps running locally"
echo ""

# Check if .env.local exists
if [ ! -f .env.local ]; then
  echo "❌ Error: .env.local file not found!"
  echo "   Please create .env.local with your environment variables"
  exit 1
fi

echo "📋 Loading CLI environment variables..."
set -a  # automatically export all variables
source .env.local
set +a  # stop automatically exporting

echo "🗄️  Starting Neo4j container..."
if ! docker compose up neo4j -d; then
  echo "❌ Failed to start Neo4j container"
  exit 1
fi

echo "⏳ Waiting for Neo4j to be ready..."
sleep 5

echo "🔧 Building packages..."
if ! pnpm build; then
  echo "❌ Failed to build packages"
  exit 1
fi

echo ""
echo "✅ Neo4j container started!"
echo "🚀 Starting applications in development mode..."
echo ""
echo "Press Ctrl+C to stop all services"
echo ""

# Function to cleanup on exit
cleanup() {
  echo ""
  echo "🛑 Stopping applications..."
  kill $(jobs -p) 2>/dev/null
  echo "✅ Applications stopped"
}

# Set trap to cleanup on script exit
trap cleanup EXIT INT TERM

# Run each app individually with environment variables to avoid TurboRepo env issues
echo "🚀 Starting individual applications with environment variables..."

# Start API with environment variables
(cd apps/api && pnpm dlx dotenv-cli -e ../../.env.local -- pnpm dev) &
API_PID=$!

# Start UI with environment variables  
(cd apps/ui && pnpm dlx dotenv-cli -e ../../.env.local -- pnpm dev) &
UI_PID=$!

# Start Auth with environment variables
(cd apps/auth && pnpm dlx dotenv-cli -e ../../.env.local -- pnpm dev) &
AUTH_PID=$!

echo "🔧 Starting package watchers..."
# Start design system watcher
(cd packages/design-system && pnpm dev) &
DESIGN_PID=$!

# Start GraphQL schema watcher
(cd packages/graphql-schema && pnpm dev) &
SCHEMA_PID=$!

echo "🔹 API will be available at: http://localhost:4000"
echo "🔹 UI will be available at: http://localhost:3001"
echo "🔹 Auth will be available at: http://localhost:3003"
echo "🔹 Neo4j Browser: http://localhost:7474"
echo ""

# Wait for all processes
wait $API_PID $UI_PID $AUTH_PID $DESIGN_PID $SCHEMA_PID

echo "🛑 Development environment stopped"