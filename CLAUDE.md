# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a TurboRepo monorepo for a Kanban application built with TypeScript, React, GraphQL, and Neo4j. The architecture consists of:

- **UI App** (`apps/ui`): Next.js React application (port 3001) using Apollo Client
- **API App** (`apps/api`): GraphQL API server (port 4000) with Neo4j database and JWT authentication
- **Auth App** (`apps/auth`): NextAuth.js OIDC authentication service (port 3003)

### Key Technologies
- **Database**: Neo4j graph database with @neo4j/graphql for GraphQL-first development
- **API**: Apollo Server with Express middleware, JWT authentication
- **Frontend**: Next.js with Apollo Client, Material-UI design system
- **Authentication**: NextAuth.js with Google OAuth support
- **Testing**: Jest for unit tests, Cypress for E2E testing

### Package Structure
- `@kanban/graphql-schema`: Shared GraphQL schema and generated types
- `@kanban/design-system`: Material-UI based component library
- `@kanban/data-access`: Neo4j database access layer
- `@kanban/tsconfig`: Shared TypeScript configurations
- `@kanban/e2e`: Cypress end-to-end tests

## Development Commands

### Primary Commands
- `pnpm dev` - Start all apps in development mode
- `pnpm build` - Build all packages and apps
- `pnpm test` - Run all tests
- `pnpm lint` - Lint all packages

### Individual App Development
- `pnpm dev:ui` - UI app only (Next.js on port 3001)
- `pnpm dev:api` - API server only (GraphQL on port 4000)
- `pnpm dev:auth` - Auth app only (NextAuth on port 3003)
- `pnpm dev:ui:watch` - UI with design system in watch mode

### Testing Commands
- `pnpm test:e2e` - Run all E2E tests
- `pnpm test:e2e:open` - Open Cypress UI
- `pnpm test:e2e:api` - Test API endpoints only
- `pnpm test:e2e:ui` - Test UI functionality only

### Schema and Code Generation
- `pnpm --filter @kanban/graphql-schema run codegen:server` - Generate server types
- `pnpm --filter @kanban/graphql-schema run codegen:client` - Generate client types

## Database Setup

The application requires Neo4j. Use Docker Compose for local development:
- Neo4j runs on ports 7474 (HTTP) and 7687 (Bolt)
- Default credentials: neo4j/localdevpassword
- APOC plugin is enabled for advanced procedures

## Authentication Flow

1. User visits UI app (localhost:3001)
2. Authentication redirects to Auth app (localhost:3003)
3. NextAuth.js handles Google OAuth
4. JWT tokens are used for API authorization
5. API validates tokens and provides user context to resolvers

## Key Files and Patterns

### API Structure
- `apps/api/src/index.ts` - Apollo Server setup with Neo4j integration
- `apps/api/src/resolvers.ts` - Custom GraphQL resolvers
- `apps/api/src/auth/jwt.ts` - JWT token handling

### GraphQL Schema
- Schema-first approach using @neo4j/graphql
- Shared types generated from schema definitions
- Client and server type generation via GraphQL Code Generator

### UI Patterns
- Apollo Client for GraphQL queries/mutations
- Material-UI design system components
- Next.js pages in `src/pages/` directory

## Environment Variables

Required for full functionality:
- `NEO4J_URI`, `NEO4J_USER`, `NEO4J_PASSWORD` - Database connection
- `JWT_SECRET` - Token signing
- `GOOGLE_CLIENT_ID`, `GOOGLE_CLIENT_SECRET` - OAuth
- `NEXTAUTH_SECRET`, `NEXTAUTH_URL` - NextAuth configuration

## Workspace Commands

When working with specific packages, use TurboRepo filters:
- `pnpm --filter @kanban/ui <command>` - Target UI app
- `pnpm --filter @kanban/api <command>` - Target API app
- `pnpm --filter @kanban/graphql-schema <command>` - Target schema package