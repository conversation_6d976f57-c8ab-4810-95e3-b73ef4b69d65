services:
  neo4j:
    image: neo4j:5.26
    container_name: kanban-neo4j
    ports:
      - "${NEO4J_PORT:-7474}:7474"
      - "${NEO4J_BOLT_PORT:-7687}:7687"
    environment:
      - NEO4J_AUTH=${NEO4J_USER}/${NEO4J_PASSWORD}
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    networks:
      - kanban-network
    restart: unless-stopped

  kanban-api:
    build:
      context: .
      dockerfile: apps/api/Dockerfile
    container_name: kanban-api
    ports:
      - "${API_PORT:-4000}:4000"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=4000
      - NEO4J_URI=${NEO4J_URI:-bolt://neo4j:7687}
      - NEO4J_USER=${NEO4J_USER}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - CORS_ORIGIN=${CORS_ORIGIN}
    depends_on:
      - neo4j
    networks:
      - kanban-network
    restart: unless-stopped

  kanban-ui:
    build:
      context: .
      dockerfile: apps/ui/Dockerfile
    container_name: kanban-ui
    ports:
      - "${UI_PORT:-3001}:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - NEXT_PUBLIC_AUTH_URL=${NEXT_PUBLIC_AUTH_URL}
    depends_on:
      - kanban-api
    networks:
      - kanban-network
    restart: unless-stopped

  kanban-auth:
    build:
      context: .
      dockerfile: apps/auth/Dockerfile
    container_name: kanban-auth
    ports:
      - "${AUTH_PORT:-3003}:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - NEXT_PUBLIC_UI_URL=${NEXT_PUBLIC_UI_URL}
      - JWT_SECRET=${JWT_SECRET}
      - SESSION_SECRET=${SESSION_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
    depends_on:
      - kanban-api
    networks:
      - kanban-network
    restart: unless-stopped

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:

networks:
  kanban-network:
    driver: bridge
