#!/bin/bash
./stop-local-dev.sh
mv ./packages/graphql-schema/src/schemas/scalar.codegenonly ./packages/graphql-schema/src/schemas/scalar.graphql
(pnpm run codegen:server) &
SERVER_CODEGEN_PID=$!
wait $SERVER_CODEGEN_PID
mv ./packages/graphql-schema/src/schemas/scalar.graphql ./packages/graphql-schema/src/schemas/scalar.codegenonly
#source ./.env.local
(cd apps/api && pnpm dlx dotenv-cli -e ../../.env.local -- pnpm dev) &
API_PID=$!
sleep 5
pnpm run codegen:client
