#!/bin/bash

echo "🐳 Starting Kanban with Docker Compose"
echo "📋 Mode: All services running in Docker containers"
echo ""
echo "🔹 API will be available at: http://localhost:4000"
echo "🔹 UI will be available at: http://localhost:3001"
echo "🔹 Auth will be available at: http://localhost:3003"
echo "🔹 Neo4j Browser: http://localhost:7474"
echo ""

# Check if .env.local exists
if [ ! -f .env.local ]; then
  echo "❌ Error: .env.local file not found!"
  echo "   Please create .env.local with your environment variables"
  exit 1
fi

echo "📋 Loading Docker environment variables..."
set -a  # automatically export all variables
source .env.local
set +a  # stop automatically exporting

echo "🐳 Starting all services with Docker Compose..."
if ! docker compose --env-file .env.local up --build -d; then
  echo "❌ Failed to start Docker services"
  exit 1
fi

echo "✅ All services started successfully!"
echo ""
echo "📋 Service URLs:"
echo "   🔹 API: http://localhost:${API_PORT:-4000}"
echo "   🔹 UI: http://localhost:${UI_PORT:-3001}"
echo "   🔹 Auth: http://localhost:${AUTH_PORT:-3003}"
echo "   🔹 Neo4j Browser: http://localhost:${NEO4J_PORT:-7474}"
echo ""
echo "📋 To stop all services: docker compose down"
echo "📋 To view logs: docker compose logs -f"

echo "🛑 Docker services stopped"