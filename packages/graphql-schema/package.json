{"name": "@kanban/graphql-schema", "version": "1.0.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./dist/client": {"import": "./dist/client.js", "require": "./dist/client.js", "types": "./dist/client.d.ts"}, "./dist/guidutils": {"import": "./dist/guidutils.js", "require": "./dist/guidutils.js", "types": "./dist/guidutils.d.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "codegen:server": "graphql-codegen --config codegen-server.yml", "codegen:client": "graphql-codegen --config codegen-client.yml"}, "dependencies": {"@graphql-tools/load-files": "^7.0.1", "@graphql-tools/merge": "^9.1.1", "graphql": "^16.11.0"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-codegen/typescript-react-apollo": "^4.3.3", "@kanban/tsconfig": "workspace:*", "@types/node": "^20.19.9", "typescript": "^5.8.3"}}