export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: string; output: string; }
};

export type Attachment = Uploadable & {
  __typename?: 'Attachment';
  attachmentList?: Maybe<AttachmentList>;
  externalId: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  uploadedBy: Scalars['ID']['output'];
  uploadedOn: Scalars['DateTime']['output'];
  url: Scalars['String']['output'];
};

export type AttachmentList = {
  __typename?: 'AttachmentList';
  attachments: Array<Attachment>;
  card?: Maybe<Card>;
  externalId: Scalars['ID']['output'];
};

/** Authentication related types */
export type AuthPayload = {
  __typename?: 'AuthPayload';
  token: Scalars['String']['output'];
  user: User;
};

export enum AuthProvider {
  Google = 'GOOGLE',
  Local = 'LOCAL'
}

export type Board = HaveLifecyle & {
  __typename?: 'Board';
  columnOrder: Array<Scalars['ID']['output']>;
  columns: Array<Column>;
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
  externalId: Scalars['ID']['output'];
  linkedCard?: Maybe<Card>;
  name: Scalars['String']['output'];
};

export type BoardList = {
  __typename?: 'BoardList';
  boards: Array<Board>;
  externalId: Scalars['ID']['output'];
};

export type Card = HaveLifecyle & {
  __typename?: 'Card';
  assignedOn?: Maybe<Scalars['DateTime']['output']>;
  assignedTo?: Maybe<Scalars['ID']['output']>;
  attachmentList?: Maybe<AttachmentList>;
  boardTags: Array<Scalars['ID']['output']>;
  color?: Maybe<Scalars['String']['output']>;
  column: Column;
  commentList?: Maybe<CommentList>;
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  dueDate?: Maybe<Scalars['String']['output']>;
  estimatedHours?: Maybe<Scalars['Float']['output']>;
  externalId: Scalars['ID']['output'];
  historyList?: Maybe<HistoryList>;
  importedId?: Maybe<Scalars['String']['output']>;
  linkedBoard?: Maybe<Board>;
  priority?: Maybe<Scalars['Int']['output']>;
  tags: Array<Scalars['String']['output']>;
  title: Scalars['String']['output'];
  workedHours?: Maybe<Scalars['Float']['output']>;
};

export type CardList = {
  __typename?: 'CardList';
  cards: Array<Card>;
  externalId: Scalars['ID']['output'];
};

export type Column = {
  __typename?: 'Column';
  board: Board;
  cardPriorityOrder: Array<Scalars['ID']['output']>;
  cards: Array<Card>;
  externalId: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  numberOfCards?: Maybe<Scalars['Int']['output']>;
  type: ColumnType;
  wipLimit?: Maybe<Scalars['Int']['output']>;
};

export enum ColumnType {
  Completed = 'COMPLETED',
  InProgress = 'IN_PROGRESS',
  NotStarted = 'NOT_STARTED'
}

export type Comment = Trackable & {
  __typename?: 'Comment';
  attachmentList?: Maybe<AttachmentList>;
  commentList?: Maybe<CommentList>;
  content: Scalars['String']['output'];
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
  externalId: Scalars['ID']['output'];
  modifiedBy?: Maybe<Scalars['ID']['output']>;
  modifiedOn?: Maybe<Scalars['DateTime']['output']>;
};

export type CommentList = {
  __typename?: 'CommentList';
  card?: Maybe<Card>;
  comments: Array<Comment>;
  externalId: Scalars['ID']['output'];
};

export type Creatable = {
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
};

export type Deletable = {
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
};

export type HaveLifecyle = {
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
};

export type History = {
  __typename?: 'History';
  after?: Maybe<Scalars['String']['output']>;
  before?: Maybe<Scalars['String']['output']>;
  dateTime: Scalars['DateTime']['output'];
  externalId: Scalars['ID']['output'];
  historyList?: Maybe<HistoryList>;
  operation: Scalars['String']['output'];
  referenceId: Scalars['ID']['output'];
};

export type HistoryList = {
  __typename?: 'HistoryList';
  card?: Maybe<Card>;
  externalId: Scalars['ID']['output'];
  histories: Array<History>;
};

export type LinkCardToBoardResult = {
  __typename?: 'LinkCardToBoardResult';
  message: Scalars['String']['output'];
  success: Scalars['Boolean']['output'];
};

export type Modifiable = {
  modifiedBy?: Maybe<Scalars['ID']['output']>;
  modifiedOn?: Maybe<Scalars['DateTime']['output']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  createOrUpdateGoogleUser?: Maybe<AuthPayload>;
  linkCardToBoard: LinkCardToBoardResult;
  signIn?: Maybe<AuthPayload>;
  signUp?: Maybe<AuthPayload>;
};


export type MutationCreateOrUpdateGoogleUserArgs = {
  email: Scalars['String']['input'];
  name: Scalars['String']['input'];
};


export type MutationLinkCardToBoardArgs = {
  boardId: Scalars['ID']['input'];
  cardId: Scalars['ID']['input'];
};


export type MutationSignInArgs = {
  input: SignInInput;
};


export type MutationSignUpArgs = {
  input: SignUpInput;
};

export type Query = {
  __typename?: 'Query';
  hello?: Maybe<Scalars['String']['output']>;
  me?: Maybe<User>;
};

export type SignInInput = {
  email: Scalars['String']['input'];
  password: Scalars['String']['input'];
};

export type SignUpInput = {
  email: Scalars['String']['input'];
  name: Scalars['String']['input'];
  password: Scalars['String']['input'];
};

export type Startable = {
  startedBy: Scalars['ID']['output'];
  startedOn: Scalars['DateTime']['output'];
};

export type Stoppable = {
  stoppedBy?: Maybe<Scalars['ID']['output']>;
  stoppedOn?: Maybe<Scalars['DateTime']['output']>;
};

export type Timeable = {
  startedBy: Scalars['ID']['output'];
  startedOn: Scalars['DateTime']['output'];
  stoppedBy?: Maybe<Scalars['ID']['output']>;
  stoppedOn?: Maybe<Scalars['DateTime']['output']>;
};

export type Trackable = {
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
  modifiedBy?: Maybe<Scalars['ID']['output']>;
  modifiedOn?: Maybe<Scalars['DateTime']['output']>;
};

export type Uploadable = {
  uploadedBy: Scalars['ID']['output'];
  uploadedOn: Scalars['DateTime']['output'];
};

export type User = HaveLifecyle & {
  __typename?: 'User';
  assignedCards: CardList;
  authMethods: Array<UserAuth>;
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
  email: Scalars['String']['output'];
  externalId: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  ownedBoards: BoardList;
};

export type UserAuth = HaveLifecyle & {
  __typename?: 'UserAuth';
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
  email: Scalars['String']['output'];
  externalId: Scalars['ID']['output'];
  password?: Maybe<Scalars['String']['output']>;
  provider: AuthProvider;
  providerUserId: Scalars['String']['output'];
  user: User;
};
