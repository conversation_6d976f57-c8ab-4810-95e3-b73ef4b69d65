query GetMeAndBoards {
  me {
    externalId
    email
    name
  }
  boards {
    externalId
    name
    createdOn
  }
}

mutation CreateBoard($input: [BoardCreateInput!]!) {
  createBoards(input: $input) {
    info {
      nodesCreated
    }
    boards {
      externalId
      name
      createdOn
    }
  }
}

mutation CreateBoardLinkedToCard(
  $boardInput: BoardCreateInput!
  $cardId: ID!
  $boardId: ID!
) {
  createBoards(input: [$boardInput]) {
    info {
      nodesCreated
    }
    boards {
      externalId
      name
      createdOn
    }
  }

  linkCardToBoard(cardId: $cardId, boardId: $boardId) {
    success
    message
  }
}

mutation CreateColumns($input: [ColumnCreateInput!]!) {
  createColumns(input: $input) {
    info {
      nodesCreated
    }
    columns {
      externalId
      name
      type
    }
  }
}
