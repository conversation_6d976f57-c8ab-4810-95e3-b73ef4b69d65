query GetBoardWithColumns($boardId: ID!) {
  boards(where: { externalId: { eq: $boardId } }) {
    externalId
    name
    columnOrder
    columns {
      externalId
      name
      type
      wipLimit
      cardPriorityOrder
      cards {
        externalId
        title
        description
        tags
        color
        priority
        estimatedHours
        workedHours
        dueDate
        createdBy
        createdOn
        assignee {
          externalId
          name
          email
        }
        linkedBoards {
          externalId
          name
        }
      }
    }
  }
  me {
    externalId
    email
    name
  }
}

mutation CreateCard($columnId: ID!, $cardInput: CardCreateInput!, $updatedCardPriorityOrder: [ID!]!) {
  updateColumns(
    where: { externalId: { eq: $columnId } }
    update: {
      cards: {
        create: [{ node: $cardInput }]
      }
      cardPriorityOrder: { set: $updatedCardPriorityOrder }
    }
  ) {
    info {
      nodesCreated
      nodesDeleted
    }
    columns {
      externalId
      cardPriorityOrder
      cards {
        externalId
        title
        description
        tags
        color
        priority
        estimatedHours
        workedHours
        dueDate
        createdBy
        createdOn
        assignee {
          externalId
          name
          email
        }
      }
    }
  }
}

mutation MoveCard($cardId: ID!, $newColumnId: ID!, $oldColumnId: ID!, $newColumnCardOrder: [ID!]!, $oldColumnCardOrder: [ID!]!) {
  # Remove card from old column and update its priority order
  updateOldColumn: updateColumns(
    where: { externalId: { eq: $oldColumnId } }
    update: {
      cards: {
        disconnect: {
          where: { node: { externalId: { eq: $cardId } } }
        }
      }
      cardPriorityOrder: { set: $oldColumnCardOrder }
    }
  ) {
    columns {
      externalId
      cardPriorityOrder
    }
  }
  
  # Add card to new column and update its priority order
  updateNewColumn: updateColumns(
    where: { externalId: { eq: $newColumnId } }
    update: {
      cards: {
        connect: {
          where: { node: { externalId: { eq: $cardId } } }
        }
      }
      cardPriorityOrder: { set: $newColumnCardOrder }
    }
  ) {
    columns {
      externalId
      cardPriorityOrder
    }
  }
}

mutation ReorderCardsInColumn($columnId: ID!, $cardPriorityOrder: [ID!]!) {
  updateColumns(
    where: { externalId: { eq: $columnId } }
    update: {
      cardPriorityOrder: { set: $cardPriorityOrder }
    }
  ) {
    info {
      nodesCreated
      nodesDeleted
    }
    columns {
      externalId
      cardPriorityOrder
    }
  }
}
