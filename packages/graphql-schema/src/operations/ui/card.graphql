query GetCardWithDetails($cardId: ID!) {
  columns(where: { cards: { some: { externalId: { eq: $cardId } } } }) {
    externalId
    name
    type
    cards(where: { externalId: { eq: $cardId } }) {
      externalId
      title
      description
      tags
      boardTags
      estimatedHours
      workedHours
      color
      dueDate
      priority
      createdBy
      createdOn
      deletedBy
      deletedOn
      
      assignee {
        externalId
        name
        email
      }
      
      commentList {
        externalId
        comments {
          externalId
          content
          createdBy
          createdOn
          modifiedBy
          modifiedOn
          deletedBy
          deletedOn
          
          attachmentList {
            externalId
            attachments {
              externalId
              name
              url
              uploadedBy
              uploadedOn
            }
          }
          
          commentList {
            externalId
            comments {
              externalId
              content
              createdBy
              createdOn
              modifiedBy
              modifiedOn
              deletedBy
              deletedOn
            }
          }
        }
      }
      
      attachmentList {
        externalId
        attachments {
          externalId
          name
          url
          uploadedBy
          uploadedOn
        }
      }
      
      historyList {
        externalId
        histories {
          externalId
          referenceId
          operation
          dateTime
          before
          after
        }
      }
    }
  }
}

mutation AddComment($commentInput: CommentCreateInput!) {
  createComments(input: [$commentInput]) {
    info {
      nodesCreated
    }
    comments {
      externalId
      content
      createdBy
      createdOn
      modifiedBy
      modifiedOn
      deletedBy
      deletedOn
      
      attachmentList {
        externalId
        attachments {
          externalId
          name
          url
          uploadedBy
          uploadedOn
        }
      }
      
      commentList {
        externalId
        comments {
          externalId
          content
          createdBy
          createdOn
          modifiedBy
          modifiedOn
          deletedBy
          deletedOn
        }
      }
    }
  }
}

mutation AddAttachment($attachmentInput: AttachmentCreateInput!) {
  createAttachments(input: [$attachmentInput]) {
    info {
      nodesCreated
    }
    attachments {
      externalId
      name
      url
      uploadedBy
      uploadedOn
    }
  }
}