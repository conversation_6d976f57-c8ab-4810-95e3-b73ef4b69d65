mutation CreateOrUpdateGoogleUser($email: String!, $name: String!) {
  createOrUpdateGoogleUser(email: $email, name: $name) {
    token
    user {
      externalId
      email
      name
    }
  }
}

mutation SignUp($input: SignUpInput!) {
  signUp(input: $input) {
    token
    user {
      externalId
      email
      name
    }
  }
}

mutation SignIn($input: SignInInput!) {
  signIn(input: $input) {
    token
    user {
      externalId
      email
      name
    }
  }
}