import { loadFilesSync } from '@graphql-tools/load-files';
import { mergeTypeDefs } from '@graphql-tools/merge';
import { DocumentNode } from 'graphql';
import { join } from 'path';

// Export only server-side schema utilities and base types
export * from "./types";

// Load all GraphQL schema files
const typesArray = loadFilesSync(join(__dirname, '..', 'src', 'schemas'), {
  extensions: ['graphql']
});

// Merge all schema files into a single schema definition
const mergedTypeDefs = mergeTypeDefs(typesArray);

// Export the merged schema as a DocumentNode for use in other packages
export const typeDefs: DocumentNode = mergedTypeDefs;
