enum ColumnType {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
}

type Column @node {
  externalId: ID!
  name: String!
  type: ColumnType!
  wipLimit: Int
  numberOfCards: Int @customResolver(requires: "externalId")

  board: Board!  #@relationship(type: "IS_STATE_IN", direction: OUT)
    @cypher(
      statement: """
      MATCH (this)-[:IS_STATE_IN]->(b:Board)
      RETURN b
      """
      columnName: "b"
    )

  cardPriorityOrder: [ID!]!
  cards: [Card!]! @relationship(type: "HAS_STATE", direction: IN)
}
