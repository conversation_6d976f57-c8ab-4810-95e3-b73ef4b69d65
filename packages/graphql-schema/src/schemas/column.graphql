enum ColumnType {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
}

type Column @node {
  # ExternalId
  # Name
  # Type (NotStarted, InProgress, Completed)
  # WipLimit
  # NumberOfCards @computed
  # CardPriorityOrder: [CardId600, CardId35, CardId42, CardId69, CardId300]
  # 	Operation: 'CardId69, Position 1'

  externalId: ID!
  name: String!
  type: ColumnType!
  wipLimit: Int
  numberOfCards: Int @customResolver(requires: "externalId")
  cardPriorityOrder: [ID!]!

  board: Board! #@relationship(type: "IS_STATE_IN", direction: OUT)

  cards: [Card!]! @relationship(type: "HAS_STATE", direction: IN)
}
