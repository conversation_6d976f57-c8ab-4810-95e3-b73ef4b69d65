type Board implements HaveL<PERSON>cyle @node {
  externalId: ID!
  name: String!

  createdOn: DateTime!
  createdBy: ID!
  deletedOn: DateTime
  deletedBy: ID

  columnOrder: [ID!]! # array of column IDs
  columns: [Column!]! @relationship(type: "IS_STATE_IN", direction: IN)

  linkedCard: Card  # @relationship(type: "IS_WORK_FOR", direction: OUT)
    @cypher(
      statement: """
      MATCH (this)-[:IS_WORK_FOR]->(c:Card)
      RETURN c
      """
      columnName: "c"
    )
}

type BoardList @node {
  externalId: ID!

  boards: [Board!]! @relationship(type: "HAS_BOARD", direction: IN)
}
