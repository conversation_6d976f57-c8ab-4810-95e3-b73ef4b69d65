type UserAuth implements <PERSON><PERSON><PERSON><PERSON><PERSON>
  @node
  @query(read: false, aggregate: false)
  @mutation(operations: [])
  @subscription(events: []) {
  externalId: ID!
  provider: AuthProvider!
  providerUserId: String!
  email: String!
  password: String
  user: User! #@relationship(type: "AUTHENTICATED_BY", direction: IN)
  createdOn: DateTime!
  createdBy: ID!
  deletedOn: DateTime
  deletedBy: ID
}
