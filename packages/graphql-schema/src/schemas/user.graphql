type User implements <PERSON><PERSON><PERSON><PERSON><PERSON>
  @node
  @query(read: false, aggregate: false)
  @mutation(operations: [])
  @subscription(events: []) {
  externalId: ID!
  email: String!
  name: String!
  authMethods: [UserAuth!]!
    @relationship(type: "AUTHENTICATED_BY", direction: OUT)

  assignedCards: CardList!
  ownedBoards: BoardList!
  createdOn: DateTime!
  createdBy: ID!
  deletedOn: DateTime
  deletedBy: ID
}
