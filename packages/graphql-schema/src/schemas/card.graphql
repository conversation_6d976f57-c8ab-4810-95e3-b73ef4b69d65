type Card implements HaveL<PERSON>cyle @node {
  externalId: ID!
  importedId: String
  title: String!
  description: String
  tags: [String!]!
  boardTags: [ID!]!
  estimatedHours: Float
  workedHours: Float
  color: String # RGB hex code
  dueDate: String
  priority: Int

  createdBy: ID!
  createdOn: DateTime!
  deletedBy: ID
  deletedOn: DateTime

  # NOTE: when we switch to cross tagging cards to boards we will need to change this
  # columns: [Column!]! @relationship(type: "HAS_STATE", direction: OUT) # a card can be linked to multiple boards' columns
  column: Column!  # @relationship(type: "HAS_STATE", direction: OUT)
    @cypher(
      statement: """
      MATCH (this)-[:HAS_STATE]->(c:Column)
      RETURN c
      """
      columnName: "c"
    )

  assignedTo: ID
  assignedOn: DateTime
  # assignee: User # Do we actually need this?

  attachmentList: AttachmentList
    @cypher(
      statement: """
      MATCH (this)<-[:ATTACHMENT_LIST_IN]-(al:AttachmentList)
      RETURN al
      """
      columnName: "al"
    )

  commentList: CommentList
    @cypher(
      statement: """
      MATCH (this)<-[:COMMENT_LIST_IN]-(cl:CommentList)
      RETURN cl
      """
      columnName: "cl"
    )

  historyList: HistoryList
    @cypher(
      statement: """
      MATCH (this)<-[:HISTORY_LIST_IN]-(hl:HistoryList)
      RETURN hl
      """
      columnName: "hl"
    )

  linkedBoard: Board  # @relationship(type: "IS_WORK_FOR", direction: IN)
    @cypher(
      statement: """
      MATCH (this)<-[:IS_WORK_FOR]-(b:Board)
      RETURN b
      """
      columnName: "b"
    )
}

type CardList @node {
  externalId: ID!

  cards: [Card!]! @relationship(type: "IN", direction: IN) ## [Card]--IN-->[CardList]
}

type HistoryList @node {
  externalId: ID!

  card: Card  # @relationship(type: "HISTORY_LIST_IN", direction: OUT)
    @cypher(
      statement: """
      MATCH (this)-[:HISTORY_LIST_IN]->(c:Card)
      RETURN c
      """
      columnName: "c"
    )

  histories: [History!]! @relationship(type: "IN", direction: IN) ## [History]--IN-->[HistoryList]
}

type History @node {
  externalId: ID!

  referenceId: ID!
  operation: String! # for now, but this should be an operation enum
  dateTime: DateTime!
  before: String
  after: String

  historyList: HistoryList
    @cypher(
      statement: """
      MATCH (this)-[:IN]->(hl:HistoryList)
      RETURN hl
      """
      columnName: "hl"
    )
}

type CommentList @node {
  externalId: ID!

  card: Card  # @relationship(type: "COMMENT_LIST_IN", direction: OUT)
    @cypher(
      statement: """
      MATCH (this)-[:COMMENT_LIST_IN]->(c:Card)
      RETURN c
      """
      columnName: "c"
    )

  comments: [Comment!]! @relationship(type: "IN", direction: IN) ## [Comment]--IN-->[CommentList]
}

type Comment implements Trackable @node {
  externalId: ID!
  content: String!
  attachmentList: AttachmentList #@relationship(type: "HAS_ATTACHMENT", direction: OUT)
  createdBy: ID!
  createdOn: DateTime!
  modifiedBy: ID
  modifiedOn: DateTime
  deletedBy: ID
  deletedOn: DateTime

  commentList: CommentList
    @cypher(
      statement: """
      MATCH (this)-[:IN]->(cl:CommentList)
      RETURN cl
      """
      columnName: "cl"
    )
}

type AttachmentList @node {
  externalId: ID!

  card: Card  # @relationship(type: "ATTACHMENT_LIST_IN", direction: OUT)
    @cypher(
      statement: """
      MATCH (this)-[:ATTACHMENT_LIST_IN]->(c:Card)
      RETURN c
      """
      columnName: "c"
    )

  attachments: [Attachment!]! @relationship(type: "IN", direction: IN) ## [Attachment]--IN-->[AttachmentList]
}

type Attachment implements Uploadable @node {
  externalId: ID!
  name: String!
  url: String!

  uploadedBy: ID!
  uploadedOn: DateTime!

  attachmentList: AttachmentList
    @cypher(
      statement: """
      MATCH (this)-[:IN]->(al:AttachmentList)
      RETURN al
      """
      columnName: "al"
    )
}
