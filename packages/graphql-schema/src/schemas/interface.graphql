interface Creatable {
  createdOn: DateTime!
  createdBy: ID!
}

interface Modifiable {
  modifiedOn: DateTime
  modifiedBy: ID
}

interface Deletable {
  deletedOn: DateTime
  deletedBy: ID
}

interface HaveLifecyle {
  createdOn: DateTime!
  createdBy: ID!
  deletedOn: DateTime
  deletedBy: ID
}

interface Trackable {
  createdOn: DateTime!
  createdBy: ID!
  modifiedOn: DateTime
  modifiedBy: ID
  deletedOn: DateTime
  deletedBy: ID
}

interface Uploadable {
  uploadedOn: DateTime!
  uploadedBy: ID!
}

interface Startable {
  startedOn: DateTime!
  startedBy: ID!
}

interface Stoppable {
  stoppedOn: DateTime
  stoppedBy: ID
}

interface Timeable {
  startedOn: DateTime!
  startedBy: ID!
  stoppedOn: DateTime
  stoppedBy: ID
}
