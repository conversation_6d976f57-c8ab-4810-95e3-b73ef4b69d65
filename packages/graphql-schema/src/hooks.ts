import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: string; output: string; }
};

export type Attachment = Uploadable & {
  __typename?: 'Attachment';
  externalId: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  uploadedBy: Scalars['ID']['output'];
  uploadedOn: Scalars['DateTime']['output'];
  url: Scalars['String']['output'];
};

export type AttachmentAggregate = {
  __typename?: 'AttachmentAggregate';
  count: Count;
  node: AttachmentAggregateNode;
};

export type AttachmentAggregateNode = {
  __typename?: 'AttachmentAggregateNode';
  name: StringAggregateSelection;
  uploadedOn: DateTimeAggregateSelection;
  url: StringAggregateSelection;
};

export type AttachmentConnectWhere = {
  node: AttachmentWhere;
};

export type AttachmentCreateInput = {
  externalId: Scalars['ID']['input'];
  name: Scalars['String']['input'];
  uploadedBy: Scalars['ID']['input'];
  uploadedOn: Scalars['DateTime']['input'];
  url: Scalars['String']['input'];
};

export type AttachmentEdge = {
  __typename?: 'AttachmentEdge';
  cursor: Scalars['String']['output'];
  node: Attachment;
};

export type AttachmentList = {
  __typename?: 'AttachmentList';
  attachments: Array<Attachment>;
  attachmentsConnection: AttachmentListAttachmentsConnection;
  externalId: Scalars['ID']['output'];
};


export type AttachmentListAttachmentsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<AttachmentSort>>;
  where?: InputMaybe<AttachmentWhere>;
};


export type AttachmentListAttachmentsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<AttachmentListAttachmentsConnectionSort>>;
  where?: InputMaybe<AttachmentListAttachmentsConnectionWhere>;
};

export type AttachmentListAggregate = {
  __typename?: 'AttachmentListAggregate';
  count: Count;
};

export type AttachmentListAttachmentAttachmentsAggregateSelection = {
  __typename?: 'AttachmentListAttachmentAttachmentsAggregateSelection';
  count: CountConnection;
  node?: Maybe<AttachmentListAttachmentAttachmentsNodeAggregateSelection>;
};

export type AttachmentListAttachmentAttachmentsNodeAggregateSelection = {
  __typename?: 'AttachmentListAttachmentAttachmentsNodeAggregateSelection';
  name: StringAggregateSelection;
  uploadedOn: DateTimeAggregateSelection;
  url: StringAggregateSelection;
};

export type AttachmentListAttachmentsAggregateInput = {
  AND?: InputMaybe<Array<AttachmentListAttachmentsAggregateInput>>;
  NOT?: InputMaybe<AttachmentListAttachmentsAggregateInput>;
  OR?: InputMaybe<Array<AttachmentListAttachmentsAggregateInput>>;
  count?: InputMaybe<IntScalarFilters>;
  count_EQ?: InputMaybe<Scalars['Int']['input']>;
  count_GT?: InputMaybe<Scalars['Int']['input']>;
  count_GTE?: InputMaybe<Scalars['Int']['input']>;
  count_LT?: InputMaybe<Scalars['Int']['input']>;
  count_LTE?: InputMaybe<Scalars['Int']['input']>;
  node?: InputMaybe<AttachmentListAttachmentsNodeAggregationWhereInput>;
};

export type AttachmentListAttachmentsConnectFieldInput = {
  where?: InputMaybe<AttachmentConnectWhere>;
};

export type AttachmentListAttachmentsConnection = {
  __typename?: 'AttachmentListAttachmentsConnection';
  aggregate: AttachmentListAttachmentAttachmentsAggregateSelection;
  edges: Array<AttachmentListAttachmentsRelationship>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type AttachmentListAttachmentsConnectionAggregateInput = {
  AND?: InputMaybe<Array<AttachmentListAttachmentsConnectionAggregateInput>>;
  NOT?: InputMaybe<AttachmentListAttachmentsConnectionAggregateInput>;
  OR?: InputMaybe<Array<AttachmentListAttachmentsConnectionAggregateInput>>;
  count?: InputMaybe<ConnectionAggregationCountFilterInput>;
  node?: InputMaybe<AttachmentListAttachmentsNodeAggregationWhereInput>;
};

export type AttachmentListAttachmentsConnectionFilters = {
  /** Filter AttachmentLists by aggregating results on related AttachmentListAttachmentsConnections */
  aggregate?: InputMaybe<AttachmentListAttachmentsConnectionAggregateInput>;
  /** Return AttachmentLists where all of the related AttachmentListAttachmentsConnections match this filter */
  all?: InputMaybe<AttachmentListAttachmentsConnectionWhere>;
  /** Return AttachmentLists where none of the related AttachmentListAttachmentsConnections match this filter */
  none?: InputMaybe<AttachmentListAttachmentsConnectionWhere>;
  /** Return AttachmentLists where one of the related AttachmentListAttachmentsConnections match this filter */
  single?: InputMaybe<AttachmentListAttachmentsConnectionWhere>;
  /** Return AttachmentLists where some of the related AttachmentListAttachmentsConnections match this filter */
  some?: InputMaybe<AttachmentListAttachmentsConnectionWhere>;
};

export type AttachmentListAttachmentsConnectionSort = {
  node?: InputMaybe<AttachmentSort>;
};

export type AttachmentListAttachmentsConnectionWhere = {
  AND?: InputMaybe<Array<AttachmentListAttachmentsConnectionWhere>>;
  NOT?: InputMaybe<AttachmentListAttachmentsConnectionWhere>;
  OR?: InputMaybe<Array<AttachmentListAttachmentsConnectionWhere>>;
  node?: InputMaybe<AttachmentWhere>;
};

export type AttachmentListAttachmentsCreateFieldInput = {
  node: AttachmentCreateInput;
};

export type AttachmentListAttachmentsDeleteFieldInput = {
  where?: InputMaybe<AttachmentListAttachmentsConnectionWhere>;
};

export type AttachmentListAttachmentsDisconnectFieldInput = {
  where?: InputMaybe<AttachmentListAttachmentsConnectionWhere>;
};

export type AttachmentListAttachmentsFieldInput = {
  connect?: InputMaybe<Array<AttachmentListAttachmentsConnectFieldInput>>;
  create?: InputMaybe<Array<AttachmentListAttachmentsCreateFieldInput>>;
};

export type AttachmentListAttachmentsNodeAggregationWhereInput = {
  AND?: InputMaybe<Array<AttachmentListAttachmentsNodeAggregationWhereInput>>;
  NOT?: InputMaybe<AttachmentListAttachmentsNodeAggregationWhereInput>;
  OR?: InputMaybe<Array<AttachmentListAttachmentsNodeAggregationWhereInput>>;
  name?: InputMaybe<StringScalarAggregationFilters>;
  uploadedOn?: InputMaybe<DateTimeScalarAggregationFilters>;
  url?: InputMaybe<StringScalarAggregationFilters>;
};

export type AttachmentListAttachmentsRelationship = {
  __typename?: 'AttachmentListAttachmentsRelationship';
  cursor: Scalars['String']['output'];
  node: Attachment;
};

export type AttachmentListAttachmentsUpdateConnectionInput = {
  node?: InputMaybe<AttachmentUpdateInput>;
  where?: InputMaybe<AttachmentListAttachmentsConnectionWhere>;
};

export type AttachmentListAttachmentsUpdateFieldInput = {
  connect?: InputMaybe<Array<AttachmentListAttachmentsConnectFieldInput>>;
  create?: InputMaybe<Array<AttachmentListAttachmentsCreateFieldInput>>;
  delete?: InputMaybe<Array<AttachmentListAttachmentsDeleteFieldInput>>;
  disconnect?: InputMaybe<Array<AttachmentListAttachmentsDisconnectFieldInput>>;
  update?: InputMaybe<AttachmentListAttachmentsUpdateConnectionInput>;
};

export type AttachmentListCreateInput = {
  attachments?: InputMaybe<AttachmentListAttachmentsFieldInput>;
  externalId: Scalars['ID']['input'];
};

export type AttachmentListDeleteInput = {
  attachments?: InputMaybe<Array<AttachmentListAttachmentsDeleteFieldInput>>;
};

export type AttachmentListEdge = {
  __typename?: 'AttachmentListEdge';
  cursor: Scalars['String']['output'];
  node: AttachmentList;
};

/** Fields to sort AttachmentLists by. The order in which sorts are applied is not guaranteed when specifying many fields in one AttachmentListSort object. */
export type AttachmentListSort = {
  externalId?: InputMaybe<SortDirection>;
};

export type AttachmentListUpdateInput = {
  attachments?: InputMaybe<Array<AttachmentListAttachmentsUpdateFieldInput>>;
  externalId?: InputMaybe<IdScalarMutations>;
};

export type AttachmentListWhere = {
  AND?: InputMaybe<Array<AttachmentListWhere>>;
  NOT?: InputMaybe<AttachmentListWhere>;
  OR?: InputMaybe<Array<AttachmentListWhere>>;
  attachments?: InputMaybe<AttachmentRelationshipFilters>;
  attachmentsConnection?: InputMaybe<AttachmentListAttachmentsConnectionFilters>;
  externalId?: InputMaybe<IdScalarFilters>;
};

export type AttachmentListsConnection = {
  __typename?: 'AttachmentListsConnection';
  aggregate: AttachmentListAggregate;
  edges: Array<AttachmentListEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type AttachmentRelationshipFilters = {
  /** Filter type where all of the related Attachments match this filter */
  all?: InputMaybe<AttachmentWhere>;
  /** Filter type where none of the related Attachments match this filter */
  none?: InputMaybe<AttachmentWhere>;
  /** Filter type where one of the related Attachments match this filter */
  single?: InputMaybe<AttachmentWhere>;
  /** Filter type where some of the related Attachments match this filter */
  some?: InputMaybe<AttachmentWhere>;
};

/** Fields to sort Attachments by. The order in which sorts are applied is not guaranteed when specifying many fields in one AttachmentSort object. */
export type AttachmentSort = {
  externalId?: InputMaybe<SortDirection>;
  name?: InputMaybe<SortDirection>;
  uploadedBy?: InputMaybe<SortDirection>;
  uploadedOn?: InputMaybe<SortDirection>;
  url?: InputMaybe<SortDirection>;
};

export type AttachmentUpdateInput = {
  externalId?: InputMaybe<IdScalarMutations>;
  name?: InputMaybe<StringScalarMutations>;
  uploadedBy?: InputMaybe<IdScalarMutations>;
  uploadedOn?: InputMaybe<DateTimeScalarMutations>;
  url?: InputMaybe<StringScalarMutations>;
};

export type AttachmentWhere = {
  AND?: InputMaybe<Array<AttachmentWhere>>;
  NOT?: InputMaybe<AttachmentWhere>;
  OR?: InputMaybe<Array<AttachmentWhere>>;
  externalId?: InputMaybe<IdScalarFilters>;
  name?: InputMaybe<StringScalarFilters>;
  uploadedBy?: InputMaybe<IdScalarFilters>;
  uploadedOn?: InputMaybe<DateTimeScalarFilters>;
  url?: InputMaybe<StringScalarFilters>;
};

export type AttachmentsConnection = {
  __typename?: 'AttachmentsConnection';
  aggregate: AttachmentAggregate;
  edges: Array<AttachmentEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

/** Authentication related types */
export type AuthPayload = {
  __typename?: 'AuthPayload';
  token: Scalars['String']['output'];
  user: User;
};

export enum AuthProvider {
  Google = 'GOOGLE',
  Local = 'LOCAL'
}

/** AuthProvider filters */
export type AuthProviderEnumScalarFilters = {
  eq?: InputMaybe<AuthProvider>;
  in?: InputMaybe<Array<AuthProvider>>;
};

export type Board = HaveLifecyle & {
  __typename?: 'Board';
  columnOrder: Array<Scalars['ID']['output']>;
  columns: Array<Column>;
  columnsConnection: BoardColumnsConnection;
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
  externalId: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};


export type BoardColumnsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<ColumnSort>>;
  where?: InputMaybe<ColumnWhere>;
};


export type BoardColumnsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<BoardColumnsConnectionSort>>;
  where?: InputMaybe<BoardColumnsConnectionWhere>;
};

export type BoardAggregate = {
  __typename?: 'BoardAggregate';
  count: Count;
  node: BoardAggregateNode;
};

export type BoardAggregateNode = {
  __typename?: 'BoardAggregateNode';
  createdOn: DateTimeAggregateSelection;
  deletedOn: DateTimeAggregateSelection;
  name: StringAggregateSelection;
};

export type BoardColumnColumnsAggregateSelection = {
  __typename?: 'BoardColumnColumnsAggregateSelection';
  count: CountConnection;
  node?: Maybe<BoardColumnColumnsNodeAggregateSelection>;
};

export type BoardColumnColumnsNodeAggregateSelection = {
  __typename?: 'BoardColumnColumnsNodeAggregateSelection';
  name: StringAggregateSelection;
  wipLimit: IntAggregateSelection;
};

export type BoardColumnsAggregateInput = {
  AND?: InputMaybe<Array<BoardColumnsAggregateInput>>;
  NOT?: InputMaybe<BoardColumnsAggregateInput>;
  OR?: InputMaybe<Array<BoardColumnsAggregateInput>>;
  count?: InputMaybe<IntScalarFilters>;
  count_EQ?: InputMaybe<Scalars['Int']['input']>;
  count_GT?: InputMaybe<Scalars['Int']['input']>;
  count_GTE?: InputMaybe<Scalars['Int']['input']>;
  count_LT?: InputMaybe<Scalars['Int']['input']>;
  count_LTE?: InputMaybe<Scalars['Int']['input']>;
  node?: InputMaybe<BoardColumnsNodeAggregationWhereInput>;
};

export type BoardColumnsConnectFieldInput = {
  connect?: InputMaybe<Array<ColumnConnectInput>>;
  where?: InputMaybe<ColumnConnectWhere>;
};

export type BoardColumnsConnection = {
  __typename?: 'BoardColumnsConnection';
  aggregate: BoardColumnColumnsAggregateSelection;
  edges: Array<BoardColumnsRelationship>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type BoardColumnsConnectionAggregateInput = {
  AND?: InputMaybe<Array<BoardColumnsConnectionAggregateInput>>;
  NOT?: InputMaybe<BoardColumnsConnectionAggregateInput>;
  OR?: InputMaybe<Array<BoardColumnsConnectionAggregateInput>>;
  count?: InputMaybe<ConnectionAggregationCountFilterInput>;
  node?: InputMaybe<BoardColumnsNodeAggregationWhereInput>;
};

export type BoardColumnsConnectionFilters = {
  /** Filter Boards by aggregating results on related BoardColumnsConnections */
  aggregate?: InputMaybe<BoardColumnsConnectionAggregateInput>;
  /** Return Boards where all of the related BoardColumnsConnections match this filter */
  all?: InputMaybe<BoardColumnsConnectionWhere>;
  /** Return Boards where none of the related BoardColumnsConnections match this filter */
  none?: InputMaybe<BoardColumnsConnectionWhere>;
  /** Return Boards where one of the related BoardColumnsConnections match this filter */
  single?: InputMaybe<BoardColumnsConnectionWhere>;
  /** Return Boards where some of the related BoardColumnsConnections match this filter */
  some?: InputMaybe<BoardColumnsConnectionWhere>;
};

export type BoardColumnsConnectionSort = {
  node?: InputMaybe<ColumnSort>;
};

export type BoardColumnsConnectionWhere = {
  AND?: InputMaybe<Array<BoardColumnsConnectionWhere>>;
  NOT?: InputMaybe<BoardColumnsConnectionWhere>;
  OR?: InputMaybe<Array<BoardColumnsConnectionWhere>>;
  node?: InputMaybe<ColumnWhere>;
};

export type BoardColumnsCreateFieldInput = {
  node: ColumnCreateInput;
};

export type BoardColumnsDeleteFieldInput = {
  delete?: InputMaybe<ColumnDeleteInput>;
  where?: InputMaybe<BoardColumnsConnectionWhere>;
};

export type BoardColumnsDisconnectFieldInput = {
  disconnect?: InputMaybe<ColumnDisconnectInput>;
  where?: InputMaybe<BoardColumnsConnectionWhere>;
};

export type BoardColumnsFieldInput = {
  connect?: InputMaybe<Array<BoardColumnsConnectFieldInput>>;
  create?: InputMaybe<Array<BoardColumnsCreateFieldInput>>;
};

export type BoardColumnsNodeAggregationWhereInput = {
  AND?: InputMaybe<Array<BoardColumnsNodeAggregationWhereInput>>;
  NOT?: InputMaybe<BoardColumnsNodeAggregationWhereInput>;
  OR?: InputMaybe<Array<BoardColumnsNodeAggregationWhereInput>>;
  name?: InputMaybe<StringScalarAggregationFilters>;
  wipLimit?: InputMaybe<IntScalarAggregationFilters>;
};

export type BoardColumnsRelationship = {
  __typename?: 'BoardColumnsRelationship';
  cursor: Scalars['String']['output'];
  node: Column;
};

export type BoardColumnsUpdateConnectionInput = {
  node?: InputMaybe<ColumnUpdateInput>;
  where?: InputMaybe<BoardColumnsConnectionWhere>;
};

export type BoardColumnsUpdateFieldInput = {
  connect?: InputMaybe<Array<BoardColumnsConnectFieldInput>>;
  create?: InputMaybe<Array<BoardColumnsCreateFieldInput>>;
  delete?: InputMaybe<Array<BoardColumnsDeleteFieldInput>>;
  disconnect?: InputMaybe<Array<BoardColumnsDisconnectFieldInput>>;
  update?: InputMaybe<BoardColumnsUpdateConnectionInput>;
};

export type BoardConnectInput = {
  columns?: InputMaybe<Array<BoardColumnsConnectFieldInput>>;
};

export type BoardConnectWhere = {
  node: BoardWhere;
};

export type BoardCreateInput = {
  columnOrder: Array<Scalars['ID']['input']>;
  columns?: InputMaybe<BoardColumnsFieldInput>;
  createdBy: Scalars['ID']['input'];
  createdOn: Scalars['DateTime']['input'];
  deletedBy?: InputMaybe<Scalars['ID']['input']>;
  deletedOn?: InputMaybe<Scalars['DateTime']['input']>;
  externalId: Scalars['ID']['input'];
  name: Scalars['String']['input'];
};

export type BoardDeleteInput = {
  columns?: InputMaybe<Array<BoardColumnsDeleteFieldInput>>;
};

export type BoardDisconnectInput = {
  columns?: InputMaybe<Array<BoardColumnsDisconnectFieldInput>>;
};

export type BoardEdge = {
  __typename?: 'BoardEdge';
  cursor: Scalars['String']['output'];
  node: Board;
};

export type BoardList = {
  __typename?: 'BoardList';
  boards: Array<Board>;
  boardsConnection: BoardListBoardsConnection;
  externalId: Scalars['ID']['output'];
};


export type BoardListBoardsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<BoardSort>>;
  where?: InputMaybe<BoardWhere>;
};


export type BoardListBoardsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<BoardListBoardsConnectionSort>>;
  where?: InputMaybe<BoardListBoardsConnectionWhere>;
};

export type BoardListAggregate = {
  __typename?: 'BoardListAggregate';
  count: Count;
};

export type BoardListBoardBoardsAggregateSelection = {
  __typename?: 'BoardListBoardBoardsAggregateSelection';
  count: CountConnection;
  node?: Maybe<BoardListBoardBoardsNodeAggregateSelection>;
};

export type BoardListBoardBoardsNodeAggregateSelection = {
  __typename?: 'BoardListBoardBoardsNodeAggregateSelection';
  createdOn: DateTimeAggregateSelection;
  deletedOn: DateTimeAggregateSelection;
  name: StringAggregateSelection;
};

export type BoardListBoardsAggregateInput = {
  AND?: InputMaybe<Array<BoardListBoardsAggregateInput>>;
  NOT?: InputMaybe<BoardListBoardsAggregateInput>;
  OR?: InputMaybe<Array<BoardListBoardsAggregateInput>>;
  count?: InputMaybe<IntScalarFilters>;
  count_EQ?: InputMaybe<Scalars['Int']['input']>;
  count_GT?: InputMaybe<Scalars['Int']['input']>;
  count_GTE?: InputMaybe<Scalars['Int']['input']>;
  count_LT?: InputMaybe<Scalars['Int']['input']>;
  count_LTE?: InputMaybe<Scalars['Int']['input']>;
  node?: InputMaybe<BoardListBoardsNodeAggregationWhereInput>;
};

export type BoardListBoardsConnectFieldInput = {
  connect?: InputMaybe<Array<BoardConnectInput>>;
  where?: InputMaybe<BoardConnectWhere>;
};

export type BoardListBoardsConnection = {
  __typename?: 'BoardListBoardsConnection';
  aggregate: BoardListBoardBoardsAggregateSelection;
  edges: Array<BoardListBoardsRelationship>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type BoardListBoardsConnectionAggregateInput = {
  AND?: InputMaybe<Array<BoardListBoardsConnectionAggregateInput>>;
  NOT?: InputMaybe<BoardListBoardsConnectionAggregateInput>;
  OR?: InputMaybe<Array<BoardListBoardsConnectionAggregateInput>>;
  count?: InputMaybe<ConnectionAggregationCountFilterInput>;
  node?: InputMaybe<BoardListBoardsNodeAggregationWhereInput>;
};

export type BoardListBoardsConnectionFilters = {
  /** Filter BoardLists by aggregating results on related BoardListBoardsConnections */
  aggregate?: InputMaybe<BoardListBoardsConnectionAggregateInput>;
  /** Return BoardLists where all of the related BoardListBoardsConnections match this filter */
  all?: InputMaybe<BoardListBoardsConnectionWhere>;
  /** Return BoardLists where none of the related BoardListBoardsConnections match this filter */
  none?: InputMaybe<BoardListBoardsConnectionWhere>;
  /** Return BoardLists where one of the related BoardListBoardsConnections match this filter */
  single?: InputMaybe<BoardListBoardsConnectionWhere>;
  /** Return BoardLists where some of the related BoardListBoardsConnections match this filter */
  some?: InputMaybe<BoardListBoardsConnectionWhere>;
};

export type BoardListBoardsConnectionSort = {
  node?: InputMaybe<BoardSort>;
};

export type BoardListBoardsConnectionWhere = {
  AND?: InputMaybe<Array<BoardListBoardsConnectionWhere>>;
  NOT?: InputMaybe<BoardListBoardsConnectionWhere>;
  OR?: InputMaybe<Array<BoardListBoardsConnectionWhere>>;
  node?: InputMaybe<BoardWhere>;
};

export type BoardListBoardsCreateFieldInput = {
  node: BoardCreateInput;
};

export type BoardListBoardsDeleteFieldInput = {
  delete?: InputMaybe<BoardDeleteInput>;
  where?: InputMaybe<BoardListBoardsConnectionWhere>;
};

export type BoardListBoardsDisconnectFieldInput = {
  disconnect?: InputMaybe<BoardDisconnectInput>;
  where?: InputMaybe<BoardListBoardsConnectionWhere>;
};

export type BoardListBoardsFieldInput = {
  connect?: InputMaybe<Array<BoardListBoardsConnectFieldInput>>;
  create?: InputMaybe<Array<BoardListBoardsCreateFieldInput>>;
};

export type BoardListBoardsNodeAggregationWhereInput = {
  AND?: InputMaybe<Array<BoardListBoardsNodeAggregationWhereInput>>;
  NOT?: InputMaybe<BoardListBoardsNodeAggregationWhereInput>;
  OR?: InputMaybe<Array<BoardListBoardsNodeAggregationWhereInput>>;
  createdOn?: InputMaybe<DateTimeScalarAggregationFilters>;
  deletedOn?: InputMaybe<DateTimeScalarAggregationFilters>;
  name?: InputMaybe<StringScalarAggregationFilters>;
};

export type BoardListBoardsRelationship = {
  __typename?: 'BoardListBoardsRelationship';
  cursor: Scalars['String']['output'];
  node: Board;
};

export type BoardListBoardsUpdateConnectionInput = {
  node?: InputMaybe<BoardUpdateInput>;
  where?: InputMaybe<BoardListBoardsConnectionWhere>;
};

export type BoardListBoardsUpdateFieldInput = {
  connect?: InputMaybe<Array<BoardListBoardsConnectFieldInput>>;
  create?: InputMaybe<Array<BoardListBoardsCreateFieldInput>>;
  delete?: InputMaybe<Array<BoardListBoardsDeleteFieldInput>>;
  disconnect?: InputMaybe<Array<BoardListBoardsDisconnectFieldInput>>;
  update?: InputMaybe<BoardListBoardsUpdateConnectionInput>;
};

export type BoardListCreateInput = {
  boards?: InputMaybe<BoardListBoardsFieldInput>;
  externalId: Scalars['ID']['input'];
};

export type BoardListDeleteInput = {
  boards?: InputMaybe<Array<BoardListBoardsDeleteFieldInput>>;
};

export type BoardListEdge = {
  __typename?: 'BoardListEdge';
  cursor: Scalars['String']['output'];
  node: BoardList;
};

/** Fields to sort BoardLists by. The order in which sorts are applied is not guaranteed when specifying many fields in one BoardListSort object. */
export type BoardListSort = {
  externalId?: InputMaybe<SortDirection>;
};

export type BoardListUpdateInput = {
  boards?: InputMaybe<Array<BoardListBoardsUpdateFieldInput>>;
  externalId?: InputMaybe<IdScalarMutations>;
};

export type BoardListWhere = {
  AND?: InputMaybe<Array<BoardListWhere>>;
  NOT?: InputMaybe<BoardListWhere>;
  OR?: InputMaybe<Array<BoardListWhere>>;
  boards?: InputMaybe<BoardRelationshipFilters>;
  boardsConnection?: InputMaybe<BoardListBoardsConnectionFilters>;
  externalId?: InputMaybe<IdScalarFilters>;
};

export type BoardListsConnection = {
  __typename?: 'BoardListsConnection';
  aggregate: BoardListAggregate;
  edges: Array<BoardListEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type BoardRelationshipFilters = {
  /** Filter type where all of the related Boards match this filter */
  all?: InputMaybe<BoardWhere>;
  /** Filter type where none of the related Boards match this filter */
  none?: InputMaybe<BoardWhere>;
  /** Filter type where one of the related Boards match this filter */
  single?: InputMaybe<BoardWhere>;
  /** Filter type where some of the related Boards match this filter */
  some?: InputMaybe<BoardWhere>;
};

/** Fields to sort Boards by. The order in which sorts are applied is not guaranteed when specifying many fields in one BoardSort object. */
export type BoardSort = {
  createdBy?: InputMaybe<SortDirection>;
  createdOn?: InputMaybe<SortDirection>;
  deletedBy?: InputMaybe<SortDirection>;
  deletedOn?: InputMaybe<SortDirection>;
  externalId?: InputMaybe<SortDirection>;
  name?: InputMaybe<SortDirection>;
};

export type BoardUpdateInput = {
  columnOrder?: InputMaybe<ListIdMutations>;
  columns?: InputMaybe<Array<BoardColumnsUpdateFieldInput>>;
  createdBy?: InputMaybe<IdScalarMutations>;
  createdOn?: InputMaybe<DateTimeScalarMutations>;
  deletedBy?: InputMaybe<IdScalarMutations>;
  deletedOn?: InputMaybe<DateTimeScalarMutations>;
  externalId?: InputMaybe<IdScalarMutations>;
  name?: InputMaybe<StringScalarMutations>;
};

export type BoardWhere = {
  AND?: InputMaybe<Array<BoardWhere>>;
  NOT?: InputMaybe<BoardWhere>;
  OR?: InputMaybe<Array<BoardWhere>>;
  columnOrder?: InputMaybe<IdListFilters>;
  columns?: InputMaybe<ColumnRelationshipFilters>;
  columnsConnection?: InputMaybe<BoardColumnsConnectionFilters>;
  createdBy?: InputMaybe<IdScalarFilters>;
  createdOn?: InputMaybe<DateTimeScalarFilters>;
  deletedBy?: InputMaybe<IdScalarFilters>;
  deletedOn?: InputMaybe<DateTimeScalarFilters>;
  externalId?: InputMaybe<IdScalarFilters>;
  name?: InputMaybe<StringScalarFilters>;
};

export type BoardsConnection = {
  __typename?: 'BoardsConnection';
  aggregate: BoardAggregate;
  edges: Array<BoardEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type Card = HaveLifecyle & {
  __typename?: 'Card';
  assignee?: Maybe<User>;
  attachmentList?: Maybe<AttachmentList>;
  boardTags: Array<Scalars['ID']['output']>;
  color?: Maybe<Scalars['String']['output']>;
  column: Column;
  commentList?: Maybe<CommentList>;
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  dueDate?: Maybe<Scalars['String']['output']>;
  estimatedHours?: Maybe<Scalars['Float']['output']>;
  externalId: Scalars['ID']['output'];
  historyList?: Maybe<HistoryList>;
  importedId?: Maybe<Scalars['String']['output']>;
  linkedBoards: Array<Board>;
  linkedBoardsConnection: CardLinkedBoardsConnection;
  priority?: Maybe<Scalars['Int']['output']>;
  tags: Array<Scalars['String']['output']>;
  title: Scalars['String']['output'];
  workedHours?: Maybe<Scalars['Float']['output']>;
};


export type CardLinkedBoardsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<BoardSort>>;
  where?: InputMaybe<BoardWhere>;
};


export type CardLinkedBoardsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<CardLinkedBoardsConnectionSort>>;
  where?: InputMaybe<CardLinkedBoardsConnectionWhere>;
};

export type CardAggregate = {
  __typename?: 'CardAggregate';
  count: Count;
  node: CardAggregateNode;
};

export type CardAggregateNode = {
  __typename?: 'CardAggregateNode';
  color: StringAggregateSelection;
  createdOn: DateTimeAggregateSelection;
  deletedOn: DateTimeAggregateSelection;
  description: StringAggregateSelection;
  dueDate: StringAggregateSelection;
  estimatedHours: FloatAggregateSelection;
  importedId: StringAggregateSelection;
  priority: IntAggregateSelection;
  title: StringAggregateSelection;
  workedHours: FloatAggregateSelection;
};

export type CardBoardLinkedBoardsAggregateSelection = {
  __typename?: 'CardBoardLinkedBoardsAggregateSelection';
  count: CountConnection;
  node?: Maybe<CardBoardLinkedBoardsNodeAggregateSelection>;
};

export type CardBoardLinkedBoardsNodeAggregateSelection = {
  __typename?: 'CardBoardLinkedBoardsNodeAggregateSelection';
  createdOn: DateTimeAggregateSelection;
  deletedOn: DateTimeAggregateSelection;
  name: StringAggregateSelection;
};

export type CardConnectInput = {
  linkedBoards?: InputMaybe<Array<CardLinkedBoardsConnectFieldInput>>;
};

export type CardConnectWhere = {
  node: CardWhere;
};

export type CardCreateInput = {
  boardTags: Array<Scalars['ID']['input']>;
  color?: InputMaybe<Scalars['String']['input']>;
  createdBy: Scalars['ID']['input'];
  createdOn: Scalars['DateTime']['input'];
  deletedBy?: InputMaybe<Scalars['ID']['input']>;
  deletedOn?: InputMaybe<Scalars['DateTime']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  dueDate?: InputMaybe<Scalars['String']['input']>;
  estimatedHours?: InputMaybe<Scalars['Float']['input']>;
  externalId: Scalars['ID']['input'];
  importedId?: InputMaybe<Scalars['String']['input']>;
  linkedBoards?: InputMaybe<CardLinkedBoardsFieldInput>;
  priority?: InputMaybe<Scalars['Int']['input']>;
  tags: Array<Scalars['String']['input']>;
  title: Scalars['String']['input'];
  workedHours?: InputMaybe<Scalars['Float']['input']>;
};

export type CardDeleteInput = {
  linkedBoards?: InputMaybe<Array<CardLinkedBoardsDeleteFieldInput>>;
};

export type CardDisconnectInput = {
  linkedBoards?: InputMaybe<Array<CardLinkedBoardsDisconnectFieldInput>>;
};

export type CardEdge = {
  __typename?: 'CardEdge';
  cursor: Scalars['String']['output'];
  node: Card;
};

export type CardLinkedBoardsAggregateInput = {
  AND?: InputMaybe<Array<CardLinkedBoardsAggregateInput>>;
  NOT?: InputMaybe<CardLinkedBoardsAggregateInput>;
  OR?: InputMaybe<Array<CardLinkedBoardsAggregateInput>>;
  count?: InputMaybe<IntScalarFilters>;
  count_EQ?: InputMaybe<Scalars['Int']['input']>;
  count_GT?: InputMaybe<Scalars['Int']['input']>;
  count_GTE?: InputMaybe<Scalars['Int']['input']>;
  count_LT?: InputMaybe<Scalars['Int']['input']>;
  count_LTE?: InputMaybe<Scalars['Int']['input']>;
  node?: InputMaybe<CardLinkedBoardsNodeAggregationWhereInput>;
};

export type CardLinkedBoardsConnectFieldInput = {
  connect?: InputMaybe<Array<BoardConnectInput>>;
  where?: InputMaybe<BoardConnectWhere>;
};

export type CardLinkedBoardsConnection = {
  __typename?: 'CardLinkedBoardsConnection';
  aggregate: CardBoardLinkedBoardsAggregateSelection;
  edges: Array<CardLinkedBoardsRelationship>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type CardLinkedBoardsConnectionAggregateInput = {
  AND?: InputMaybe<Array<CardLinkedBoardsConnectionAggregateInput>>;
  NOT?: InputMaybe<CardLinkedBoardsConnectionAggregateInput>;
  OR?: InputMaybe<Array<CardLinkedBoardsConnectionAggregateInput>>;
  count?: InputMaybe<ConnectionAggregationCountFilterInput>;
  node?: InputMaybe<CardLinkedBoardsNodeAggregationWhereInput>;
};

export type CardLinkedBoardsConnectionFilters = {
  /** Filter Cards by aggregating results on related CardLinkedBoardsConnections */
  aggregate?: InputMaybe<CardLinkedBoardsConnectionAggregateInput>;
  /** Return Cards where all of the related CardLinkedBoardsConnections match this filter */
  all?: InputMaybe<CardLinkedBoardsConnectionWhere>;
  /** Return Cards where none of the related CardLinkedBoardsConnections match this filter */
  none?: InputMaybe<CardLinkedBoardsConnectionWhere>;
  /** Return Cards where one of the related CardLinkedBoardsConnections match this filter */
  single?: InputMaybe<CardLinkedBoardsConnectionWhere>;
  /** Return Cards where some of the related CardLinkedBoardsConnections match this filter */
  some?: InputMaybe<CardLinkedBoardsConnectionWhere>;
};

export type CardLinkedBoardsConnectionSort = {
  node?: InputMaybe<BoardSort>;
};

export type CardLinkedBoardsConnectionWhere = {
  AND?: InputMaybe<Array<CardLinkedBoardsConnectionWhere>>;
  NOT?: InputMaybe<CardLinkedBoardsConnectionWhere>;
  OR?: InputMaybe<Array<CardLinkedBoardsConnectionWhere>>;
  node?: InputMaybe<BoardWhere>;
};

export type CardLinkedBoardsCreateFieldInput = {
  node: BoardCreateInput;
};

export type CardLinkedBoardsDeleteFieldInput = {
  delete?: InputMaybe<BoardDeleteInput>;
  where?: InputMaybe<CardLinkedBoardsConnectionWhere>;
};

export type CardLinkedBoardsDisconnectFieldInput = {
  disconnect?: InputMaybe<BoardDisconnectInput>;
  where?: InputMaybe<CardLinkedBoardsConnectionWhere>;
};

export type CardLinkedBoardsFieldInput = {
  connect?: InputMaybe<Array<CardLinkedBoardsConnectFieldInput>>;
  create?: InputMaybe<Array<CardLinkedBoardsCreateFieldInput>>;
};

export type CardLinkedBoardsNodeAggregationWhereInput = {
  AND?: InputMaybe<Array<CardLinkedBoardsNodeAggregationWhereInput>>;
  NOT?: InputMaybe<CardLinkedBoardsNodeAggregationWhereInput>;
  OR?: InputMaybe<Array<CardLinkedBoardsNodeAggregationWhereInput>>;
  createdOn?: InputMaybe<DateTimeScalarAggregationFilters>;
  deletedOn?: InputMaybe<DateTimeScalarAggregationFilters>;
  name?: InputMaybe<StringScalarAggregationFilters>;
};

export type CardLinkedBoardsRelationship = {
  __typename?: 'CardLinkedBoardsRelationship';
  cursor: Scalars['String']['output'];
  node: Board;
};

export type CardLinkedBoardsUpdateConnectionInput = {
  node?: InputMaybe<BoardUpdateInput>;
  where?: InputMaybe<CardLinkedBoardsConnectionWhere>;
};

export type CardLinkedBoardsUpdateFieldInput = {
  connect?: InputMaybe<Array<CardLinkedBoardsConnectFieldInput>>;
  create?: InputMaybe<Array<CardLinkedBoardsCreateFieldInput>>;
  delete?: InputMaybe<Array<CardLinkedBoardsDeleteFieldInput>>;
  disconnect?: InputMaybe<Array<CardLinkedBoardsDisconnectFieldInput>>;
  update?: InputMaybe<CardLinkedBoardsUpdateConnectionInput>;
};

export type CardList = {
  __typename?: 'CardList';
  cards: Array<Card>;
  cardsConnection: CardListCardsConnection;
  externalId: Scalars['ID']['output'];
};


export type CardListCardsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<CardSort>>;
  where?: InputMaybe<CardWhere>;
};


export type CardListCardsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<CardListCardsConnectionSort>>;
  where?: InputMaybe<CardListCardsConnectionWhere>;
};

export type CardListAggregate = {
  __typename?: 'CardListAggregate';
  count: Count;
};

export type CardListCardCardsAggregateSelection = {
  __typename?: 'CardListCardCardsAggregateSelection';
  count: CountConnection;
  node?: Maybe<CardListCardCardsNodeAggregateSelection>;
};

export type CardListCardCardsNodeAggregateSelection = {
  __typename?: 'CardListCardCardsNodeAggregateSelection';
  color: StringAggregateSelection;
  createdOn: DateTimeAggregateSelection;
  deletedOn: DateTimeAggregateSelection;
  description: StringAggregateSelection;
  dueDate: StringAggregateSelection;
  estimatedHours: FloatAggregateSelection;
  importedId: StringAggregateSelection;
  priority: IntAggregateSelection;
  title: StringAggregateSelection;
  workedHours: FloatAggregateSelection;
};

export type CardListCardsAggregateInput = {
  AND?: InputMaybe<Array<CardListCardsAggregateInput>>;
  NOT?: InputMaybe<CardListCardsAggregateInput>;
  OR?: InputMaybe<Array<CardListCardsAggregateInput>>;
  count?: InputMaybe<IntScalarFilters>;
  count_EQ?: InputMaybe<Scalars['Int']['input']>;
  count_GT?: InputMaybe<Scalars['Int']['input']>;
  count_GTE?: InputMaybe<Scalars['Int']['input']>;
  count_LT?: InputMaybe<Scalars['Int']['input']>;
  count_LTE?: InputMaybe<Scalars['Int']['input']>;
  node?: InputMaybe<CardListCardsNodeAggregationWhereInput>;
};

export type CardListCardsConnectFieldInput = {
  connect?: InputMaybe<Array<CardConnectInput>>;
  where?: InputMaybe<CardConnectWhere>;
};

export type CardListCardsConnection = {
  __typename?: 'CardListCardsConnection';
  aggregate: CardListCardCardsAggregateSelection;
  edges: Array<CardListCardsRelationship>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type CardListCardsConnectionAggregateInput = {
  AND?: InputMaybe<Array<CardListCardsConnectionAggregateInput>>;
  NOT?: InputMaybe<CardListCardsConnectionAggregateInput>;
  OR?: InputMaybe<Array<CardListCardsConnectionAggregateInput>>;
  count?: InputMaybe<ConnectionAggregationCountFilterInput>;
  node?: InputMaybe<CardListCardsNodeAggregationWhereInput>;
};

export type CardListCardsConnectionFilters = {
  /** Filter CardLists by aggregating results on related CardListCardsConnections */
  aggregate?: InputMaybe<CardListCardsConnectionAggregateInput>;
  /** Return CardLists where all of the related CardListCardsConnections match this filter */
  all?: InputMaybe<CardListCardsConnectionWhere>;
  /** Return CardLists where none of the related CardListCardsConnections match this filter */
  none?: InputMaybe<CardListCardsConnectionWhere>;
  /** Return CardLists where one of the related CardListCardsConnections match this filter */
  single?: InputMaybe<CardListCardsConnectionWhere>;
  /** Return CardLists where some of the related CardListCardsConnections match this filter */
  some?: InputMaybe<CardListCardsConnectionWhere>;
};

export type CardListCardsConnectionSort = {
  node?: InputMaybe<CardSort>;
};

export type CardListCardsConnectionWhere = {
  AND?: InputMaybe<Array<CardListCardsConnectionWhere>>;
  NOT?: InputMaybe<CardListCardsConnectionWhere>;
  OR?: InputMaybe<Array<CardListCardsConnectionWhere>>;
  node?: InputMaybe<CardWhere>;
};

export type CardListCardsCreateFieldInput = {
  node: CardCreateInput;
};

export type CardListCardsDeleteFieldInput = {
  delete?: InputMaybe<CardDeleteInput>;
  where?: InputMaybe<CardListCardsConnectionWhere>;
};

export type CardListCardsDisconnectFieldInput = {
  disconnect?: InputMaybe<CardDisconnectInput>;
  where?: InputMaybe<CardListCardsConnectionWhere>;
};

export type CardListCardsFieldInput = {
  connect?: InputMaybe<Array<CardListCardsConnectFieldInput>>;
  create?: InputMaybe<Array<CardListCardsCreateFieldInput>>;
};

export type CardListCardsNodeAggregationWhereInput = {
  AND?: InputMaybe<Array<CardListCardsNodeAggregationWhereInput>>;
  NOT?: InputMaybe<CardListCardsNodeAggregationWhereInput>;
  OR?: InputMaybe<Array<CardListCardsNodeAggregationWhereInput>>;
  color?: InputMaybe<StringScalarAggregationFilters>;
  createdOn?: InputMaybe<DateTimeScalarAggregationFilters>;
  deletedOn?: InputMaybe<DateTimeScalarAggregationFilters>;
  description?: InputMaybe<StringScalarAggregationFilters>;
  dueDate?: InputMaybe<StringScalarAggregationFilters>;
  estimatedHours?: InputMaybe<FloatScalarAggregationFilters>;
  importedId?: InputMaybe<StringScalarAggregationFilters>;
  priority?: InputMaybe<IntScalarAggregationFilters>;
  title?: InputMaybe<StringScalarAggregationFilters>;
  workedHours?: InputMaybe<FloatScalarAggregationFilters>;
};

export type CardListCardsRelationship = {
  __typename?: 'CardListCardsRelationship';
  cursor: Scalars['String']['output'];
  node: Card;
};

export type CardListCardsUpdateConnectionInput = {
  node?: InputMaybe<CardUpdateInput>;
  where?: InputMaybe<CardListCardsConnectionWhere>;
};

export type CardListCardsUpdateFieldInput = {
  connect?: InputMaybe<Array<CardListCardsConnectFieldInput>>;
  create?: InputMaybe<Array<CardListCardsCreateFieldInput>>;
  delete?: InputMaybe<Array<CardListCardsDeleteFieldInput>>;
  disconnect?: InputMaybe<Array<CardListCardsDisconnectFieldInput>>;
  update?: InputMaybe<CardListCardsUpdateConnectionInput>;
};

export type CardListCreateInput = {
  cards?: InputMaybe<CardListCardsFieldInput>;
  externalId: Scalars['ID']['input'];
};

export type CardListDeleteInput = {
  cards?: InputMaybe<Array<CardListCardsDeleteFieldInput>>;
};

export type CardListEdge = {
  __typename?: 'CardListEdge';
  cursor: Scalars['String']['output'];
  node: CardList;
};

/** Fields to sort CardLists by. The order in which sorts are applied is not guaranteed when specifying many fields in one CardListSort object. */
export type CardListSort = {
  externalId?: InputMaybe<SortDirection>;
};

export type CardListUpdateInput = {
  cards?: InputMaybe<Array<CardListCardsUpdateFieldInput>>;
  externalId?: InputMaybe<IdScalarMutations>;
};

export type CardListWhere = {
  AND?: InputMaybe<Array<CardListWhere>>;
  NOT?: InputMaybe<CardListWhere>;
  OR?: InputMaybe<Array<CardListWhere>>;
  cards?: InputMaybe<CardRelationshipFilters>;
  cardsConnection?: InputMaybe<CardListCardsConnectionFilters>;
  externalId?: InputMaybe<IdScalarFilters>;
};

export type CardListsConnection = {
  __typename?: 'CardListsConnection';
  aggregate: CardListAggregate;
  edges: Array<CardListEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type CardRelationshipFilters = {
  /** Filter type where all of the related Cards match this filter */
  all?: InputMaybe<CardWhere>;
  /** Filter type where none of the related Cards match this filter */
  none?: InputMaybe<CardWhere>;
  /** Filter type where one of the related Cards match this filter */
  single?: InputMaybe<CardWhere>;
  /** Filter type where some of the related Cards match this filter */
  some?: InputMaybe<CardWhere>;
};

/** Fields to sort Cards by. The order in which sorts are applied is not guaranteed when specifying many fields in one CardSort object. */
export type CardSort = {
  color?: InputMaybe<SortDirection>;
  createdBy?: InputMaybe<SortDirection>;
  createdOn?: InputMaybe<SortDirection>;
  deletedBy?: InputMaybe<SortDirection>;
  deletedOn?: InputMaybe<SortDirection>;
  description?: InputMaybe<SortDirection>;
  dueDate?: InputMaybe<SortDirection>;
  estimatedHours?: InputMaybe<SortDirection>;
  externalId?: InputMaybe<SortDirection>;
  importedId?: InputMaybe<SortDirection>;
  priority?: InputMaybe<SortDirection>;
  title?: InputMaybe<SortDirection>;
  workedHours?: InputMaybe<SortDirection>;
};

export type CardUpdateInput = {
  boardTags?: InputMaybe<ListIdMutations>;
  color?: InputMaybe<StringScalarMutations>;
  createdBy?: InputMaybe<IdScalarMutations>;
  createdOn?: InputMaybe<DateTimeScalarMutations>;
  deletedBy?: InputMaybe<IdScalarMutations>;
  deletedOn?: InputMaybe<DateTimeScalarMutations>;
  description?: InputMaybe<StringScalarMutations>;
  dueDate?: InputMaybe<StringScalarMutations>;
  estimatedHours?: InputMaybe<FloatScalarMutations>;
  externalId?: InputMaybe<IdScalarMutations>;
  importedId?: InputMaybe<StringScalarMutations>;
  linkedBoards?: InputMaybe<Array<CardLinkedBoardsUpdateFieldInput>>;
  priority?: InputMaybe<IntScalarMutations>;
  tags?: InputMaybe<ListStringMutations>;
  title?: InputMaybe<StringScalarMutations>;
  workedHours?: InputMaybe<FloatScalarMutations>;
};

export type CardWhere = {
  AND?: InputMaybe<Array<CardWhere>>;
  NOT?: InputMaybe<CardWhere>;
  OR?: InputMaybe<Array<CardWhere>>;
  boardTags?: InputMaybe<IdListFilters>;
  color?: InputMaybe<StringScalarFilters>;
  createdBy?: InputMaybe<IdScalarFilters>;
  createdOn?: InputMaybe<DateTimeScalarFilters>;
  deletedBy?: InputMaybe<IdScalarFilters>;
  deletedOn?: InputMaybe<DateTimeScalarFilters>;
  description?: InputMaybe<StringScalarFilters>;
  dueDate?: InputMaybe<StringScalarFilters>;
  estimatedHours?: InputMaybe<FloatScalarFilters>;
  externalId?: InputMaybe<IdScalarFilters>;
  importedId?: InputMaybe<StringScalarFilters>;
  linkedBoards?: InputMaybe<BoardRelationshipFilters>;
  linkedBoardsConnection?: InputMaybe<CardLinkedBoardsConnectionFilters>;
  priority?: InputMaybe<IntScalarFilters>;
  tags?: InputMaybe<StringListFilters>;
  title?: InputMaybe<StringScalarFilters>;
  workedHours?: InputMaybe<FloatScalarFilters>;
};

export type CardsConnection = {
  __typename?: 'CardsConnection';
  aggregate: CardAggregate;
  edges: Array<CardEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type Column = {
  __typename?: 'Column';
  board: Board;
  cardPriorityOrder: Array<Scalars['ID']['output']>;
  cards: Array<Card>;
  cardsConnection: ColumnCardsConnection;
  externalId: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  numberOfCards?: Maybe<Scalars['Int']['output']>;
  type: ColumnType;
  wipLimit?: Maybe<Scalars['Int']['output']>;
};


export type ColumnCardsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<CardSort>>;
  where?: InputMaybe<CardWhere>;
};


export type ColumnCardsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<ColumnCardsConnectionSort>>;
  where?: InputMaybe<ColumnCardsConnectionWhere>;
};

export type ColumnAggregate = {
  __typename?: 'ColumnAggregate';
  count: Count;
  node: ColumnAggregateNode;
};

export type ColumnAggregateNode = {
  __typename?: 'ColumnAggregateNode';
  name: StringAggregateSelection;
  wipLimit: IntAggregateSelection;
};

export type ColumnCardCardsAggregateSelection = {
  __typename?: 'ColumnCardCardsAggregateSelection';
  count: CountConnection;
  node?: Maybe<ColumnCardCardsNodeAggregateSelection>;
};

export type ColumnCardCardsNodeAggregateSelection = {
  __typename?: 'ColumnCardCardsNodeAggregateSelection';
  color: StringAggregateSelection;
  createdOn: DateTimeAggregateSelection;
  deletedOn: DateTimeAggregateSelection;
  description: StringAggregateSelection;
  dueDate: StringAggregateSelection;
  estimatedHours: FloatAggregateSelection;
  importedId: StringAggregateSelection;
  priority: IntAggregateSelection;
  title: StringAggregateSelection;
  workedHours: FloatAggregateSelection;
};

export type ColumnCardsAggregateInput = {
  AND?: InputMaybe<Array<ColumnCardsAggregateInput>>;
  NOT?: InputMaybe<ColumnCardsAggregateInput>;
  OR?: InputMaybe<Array<ColumnCardsAggregateInput>>;
  count?: InputMaybe<IntScalarFilters>;
  count_EQ?: InputMaybe<Scalars['Int']['input']>;
  count_GT?: InputMaybe<Scalars['Int']['input']>;
  count_GTE?: InputMaybe<Scalars['Int']['input']>;
  count_LT?: InputMaybe<Scalars['Int']['input']>;
  count_LTE?: InputMaybe<Scalars['Int']['input']>;
  node?: InputMaybe<ColumnCardsNodeAggregationWhereInput>;
};

export type ColumnCardsConnectFieldInput = {
  connect?: InputMaybe<Array<CardConnectInput>>;
  where?: InputMaybe<CardConnectWhere>;
};

export type ColumnCardsConnection = {
  __typename?: 'ColumnCardsConnection';
  aggregate: ColumnCardCardsAggregateSelection;
  edges: Array<ColumnCardsRelationship>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type ColumnCardsConnectionAggregateInput = {
  AND?: InputMaybe<Array<ColumnCardsConnectionAggregateInput>>;
  NOT?: InputMaybe<ColumnCardsConnectionAggregateInput>;
  OR?: InputMaybe<Array<ColumnCardsConnectionAggregateInput>>;
  count?: InputMaybe<ConnectionAggregationCountFilterInput>;
  node?: InputMaybe<ColumnCardsNodeAggregationWhereInput>;
};

export type ColumnCardsConnectionFilters = {
  /** Filter Columns by aggregating results on related ColumnCardsConnections */
  aggregate?: InputMaybe<ColumnCardsConnectionAggregateInput>;
  /** Return Columns where all of the related ColumnCardsConnections match this filter */
  all?: InputMaybe<ColumnCardsConnectionWhere>;
  /** Return Columns where none of the related ColumnCardsConnections match this filter */
  none?: InputMaybe<ColumnCardsConnectionWhere>;
  /** Return Columns where one of the related ColumnCardsConnections match this filter */
  single?: InputMaybe<ColumnCardsConnectionWhere>;
  /** Return Columns where some of the related ColumnCardsConnections match this filter */
  some?: InputMaybe<ColumnCardsConnectionWhere>;
};

export type ColumnCardsConnectionSort = {
  node?: InputMaybe<CardSort>;
};

export type ColumnCardsConnectionWhere = {
  AND?: InputMaybe<Array<ColumnCardsConnectionWhere>>;
  NOT?: InputMaybe<ColumnCardsConnectionWhere>;
  OR?: InputMaybe<Array<ColumnCardsConnectionWhere>>;
  node?: InputMaybe<CardWhere>;
};

export type ColumnCardsCreateFieldInput = {
  node: CardCreateInput;
};

export type ColumnCardsDeleteFieldInput = {
  delete?: InputMaybe<CardDeleteInput>;
  where?: InputMaybe<ColumnCardsConnectionWhere>;
};

export type ColumnCardsDisconnectFieldInput = {
  disconnect?: InputMaybe<CardDisconnectInput>;
  where?: InputMaybe<ColumnCardsConnectionWhere>;
};

export type ColumnCardsFieldInput = {
  connect?: InputMaybe<Array<ColumnCardsConnectFieldInput>>;
  create?: InputMaybe<Array<ColumnCardsCreateFieldInput>>;
};

export type ColumnCardsNodeAggregationWhereInput = {
  AND?: InputMaybe<Array<ColumnCardsNodeAggregationWhereInput>>;
  NOT?: InputMaybe<ColumnCardsNodeAggregationWhereInput>;
  OR?: InputMaybe<Array<ColumnCardsNodeAggregationWhereInput>>;
  color?: InputMaybe<StringScalarAggregationFilters>;
  createdOn?: InputMaybe<DateTimeScalarAggregationFilters>;
  deletedOn?: InputMaybe<DateTimeScalarAggregationFilters>;
  description?: InputMaybe<StringScalarAggregationFilters>;
  dueDate?: InputMaybe<StringScalarAggregationFilters>;
  estimatedHours?: InputMaybe<FloatScalarAggregationFilters>;
  importedId?: InputMaybe<StringScalarAggregationFilters>;
  priority?: InputMaybe<IntScalarAggregationFilters>;
  title?: InputMaybe<StringScalarAggregationFilters>;
  workedHours?: InputMaybe<FloatScalarAggregationFilters>;
};

export type ColumnCardsRelationship = {
  __typename?: 'ColumnCardsRelationship';
  cursor: Scalars['String']['output'];
  node: Card;
};

export type ColumnCardsUpdateConnectionInput = {
  node?: InputMaybe<CardUpdateInput>;
  where?: InputMaybe<ColumnCardsConnectionWhere>;
};

export type ColumnCardsUpdateFieldInput = {
  connect?: InputMaybe<Array<ColumnCardsConnectFieldInput>>;
  create?: InputMaybe<Array<ColumnCardsCreateFieldInput>>;
  delete?: InputMaybe<Array<ColumnCardsDeleteFieldInput>>;
  disconnect?: InputMaybe<Array<ColumnCardsDisconnectFieldInput>>;
  update?: InputMaybe<ColumnCardsUpdateConnectionInput>;
};

export type ColumnConnectInput = {
  cards?: InputMaybe<Array<ColumnCardsConnectFieldInput>>;
};

export type ColumnConnectWhere = {
  node: ColumnWhere;
};

export type ColumnCreateInput = {
  cardPriorityOrder: Array<Scalars['ID']['input']>;
  cards?: InputMaybe<ColumnCardsFieldInput>;
  externalId: Scalars['ID']['input'];
  name: Scalars['String']['input'];
  type: ColumnType;
  wipLimit?: InputMaybe<Scalars['Int']['input']>;
};

export type ColumnDeleteInput = {
  cards?: InputMaybe<Array<ColumnCardsDeleteFieldInput>>;
};

export type ColumnDisconnectInput = {
  cards?: InputMaybe<Array<ColumnCardsDisconnectFieldInput>>;
};

export type ColumnEdge = {
  __typename?: 'ColumnEdge';
  cursor: Scalars['String']['output'];
  node: Column;
};

export type ColumnRelationshipFilters = {
  /** Filter type where all of the related Columns match this filter */
  all?: InputMaybe<ColumnWhere>;
  /** Filter type where none of the related Columns match this filter */
  none?: InputMaybe<ColumnWhere>;
  /** Filter type where one of the related Columns match this filter */
  single?: InputMaybe<ColumnWhere>;
  /** Filter type where some of the related Columns match this filter */
  some?: InputMaybe<ColumnWhere>;
};

/** Fields to sort Columns by. The order in which sorts are applied is not guaranteed when specifying many fields in one ColumnSort object. */
export type ColumnSort = {
  externalId?: InputMaybe<SortDirection>;
  name?: InputMaybe<SortDirection>;
  type?: InputMaybe<SortDirection>;
  wipLimit?: InputMaybe<SortDirection>;
};

export enum ColumnType {
  Completed = 'COMPLETED',
  InProgress = 'IN_PROGRESS',
  NotStarted = 'NOT_STARTED'
}

/** ColumnType filters */
export type ColumnTypeEnumScalarFilters = {
  eq?: InputMaybe<ColumnType>;
  in?: InputMaybe<Array<ColumnType>>;
};

/** ColumnType mutations */
export type ColumnTypeEnumScalarMutations = {
  set?: InputMaybe<ColumnType>;
};

export type ColumnUpdateInput = {
  cardPriorityOrder?: InputMaybe<ListIdMutations>;
  cards?: InputMaybe<Array<ColumnCardsUpdateFieldInput>>;
  externalId?: InputMaybe<IdScalarMutations>;
  name?: InputMaybe<StringScalarMutations>;
  type?: InputMaybe<ColumnTypeEnumScalarMutations>;
  wipLimit?: InputMaybe<IntScalarMutations>;
};

export type ColumnWhere = {
  AND?: InputMaybe<Array<ColumnWhere>>;
  NOT?: InputMaybe<ColumnWhere>;
  OR?: InputMaybe<Array<ColumnWhere>>;
  cardPriorityOrder?: InputMaybe<IdListFilters>;
  cards?: InputMaybe<CardRelationshipFilters>;
  cardsConnection?: InputMaybe<ColumnCardsConnectionFilters>;
  externalId?: InputMaybe<IdScalarFilters>;
  name?: InputMaybe<StringScalarFilters>;
  type?: InputMaybe<ColumnTypeEnumScalarFilters>;
  wipLimit?: InputMaybe<IntScalarFilters>;
};

export type ColumnsConnection = {
  __typename?: 'ColumnsConnection';
  aggregate: ColumnAggregate;
  edges: Array<ColumnEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type Comment = Trackable & {
  __typename?: 'Comment';
  attachmentList?: Maybe<AttachmentList>;
  commentList: CommentList;
  content: Scalars['String']['output'];
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
  externalId: Scalars['ID']['output'];
  modifiedBy?: Maybe<Scalars['ID']['output']>;
  modifiedOn?: Maybe<Scalars['DateTime']['output']>;
};

export type CommentAggregate = {
  __typename?: 'CommentAggregate';
  count: Count;
  node: CommentAggregateNode;
};

export type CommentAggregateNode = {
  __typename?: 'CommentAggregateNode';
  content: StringAggregateSelection;
  createdOn: DateTimeAggregateSelection;
  deletedOn: DateTimeAggregateSelection;
  modifiedOn: DateTimeAggregateSelection;
};

export type CommentConnectWhere = {
  node: CommentWhere;
};

export type CommentCreateInput = {
  content: Scalars['String']['input'];
  createdBy: Scalars['ID']['input'];
  createdOn: Scalars['DateTime']['input'];
  deletedBy?: InputMaybe<Scalars['ID']['input']>;
  deletedOn?: InputMaybe<Scalars['DateTime']['input']>;
  externalId: Scalars['ID']['input'];
  modifiedBy?: InputMaybe<Scalars['ID']['input']>;
  modifiedOn?: InputMaybe<Scalars['DateTime']['input']>;
};

export type CommentEdge = {
  __typename?: 'CommentEdge';
  cursor: Scalars['String']['output'];
  node: Comment;
};

export type CommentList = {
  __typename?: 'CommentList';
  comments: Array<Comment>;
  commentsConnection: CommentListCommentsConnection;
  externalId: Scalars['ID']['output'];
};


export type CommentListCommentsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<CommentSort>>;
  where?: InputMaybe<CommentWhere>;
};


export type CommentListCommentsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<CommentListCommentsConnectionSort>>;
  where?: InputMaybe<CommentListCommentsConnectionWhere>;
};

export type CommentListAggregate = {
  __typename?: 'CommentListAggregate';
  count: Count;
};

export type CommentListCommentCommentsAggregateSelection = {
  __typename?: 'CommentListCommentCommentsAggregateSelection';
  count: CountConnection;
  node?: Maybe<CommentListCommentCommentsNodeAggregateSelection>;
};

export type CommentListCommentCommentsNodeAggregateSelection = {
  __typename?: 'CommentListCommentCommentsNodeAggregateSelection';
  content: StringAggregateSelection;
  createdOn: DateTimeAggregateSelection;
  deletedOn: DateTimeAggregateSelection;
  modifiedOn: DateTimeAggregateSelection;
};

export type CommentListCommentsAggregateInput = {
  AND?: InputMaybe<Array<CommentListCommentsAggregateInput>>;
  NOT?: InputMaybe<CommentListCommentsAggregateInput>;
  OR?: InputMaybe<Array<CommentListCommentsAggregateInput>>;
  count?: InputMaybe<IntScalarFilters>;
  count_EQ?: InputMaybe<Scalars['Int']['input']>;
  count_GT?: InputMaybe<Scalars['Int']['input']>;
  count_GTE?: InputMaybe<Scalars['Int']['input']>;
  count_LT?: InputMaybe<Scalars['Int']['input']>;
  count_LTE?: InputMaybe<Scalars['Int']['input']>;
  node?: InputMaybe<CommentListCommentsNodeAggregationWhereInput>;
};

export type CommentListCommentsConnectFieldInput = {
  where?: InputMaybe<CommentConnectWhere>;
};

export type CommentListCommentsConnection = {
  __typename?: 'CommentListCommentsConnection';
  aggregate: CommentListCommentCommentsAggregateSelection;
  edges: Array<CommentListCommentsRelationship>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type CommentListCommentsConnectionAggregateInput = {
  AND?: InputMaybe<Array<CommentListCommentsConnectionAggregateInput>>;
  NOT?: InputMaybe<CommentListCommentsConnectionAggregateInput>;
  OR?: InputMaybe<Array<CommentListCommentsConnectionAggregateInput>>;
  count?: InputMaybe<ConnectionAggregationCountFilterInput>;
  node?: InputMaybe<CommentListCommentsNodeAggregationWhereInput>;
};

export type CommentListCommentsConnectionFilters = {
  /** Filter CommentLists by aggregating results on related CommentListCommentsConnections */
  aggregate?: InputMaybe<CommentListCommentsConnectionAggregateInput>;
  /** Return CommentLists where all of the related CommentListCommentsConnections match this filter */
  all?: InputMaybe<CommentListCommentsConnectionWhere>;
  /** Return CommentLists where none of the related CommentListCommentsConnections match this filter */
  none?: InputMaybe<CommentListCommentsConnectionWhere>;
  /** Return CommentLists where one of the related CommentListCommentsConnections match this filter */
  single?: InputMaybe<CommentListCommentsConnectionWhere>;
  /** Return CommentLists where some of the related CommentListCommentsConnections match this filter */
  some?: InputMaybe<CommentListCommentsConnectionWhere>;
};

export type CommentListCommentsConnectionSort = {
  node?: InputMaybe<CommentSort>;
};

export type CommentListCommentsConnectionWhere = {
  AND?: InputMaybe<Array<CommentListCommentsConnectionWhere>>;
  NOT?: InputMaybe<CommentListCommentsConnectionWhere>;
  OR?: InputMaybe<Array<CommentListCommentsConnectionWhere>>;
  node?: InputMaybe<CommentWhere>;
};

export type CommentListCommentsCreateFieldInput = {
  node: CommentCreateInput;
};

export type CommentListCommentsDeleteFieldInput = {
  where?: InputMaybe<CommentListCommentsConnectionWhere>;
};

export type CommentListCommentsDisconnectFieldInput = {
  where?: InputMaybe<CommentListCommentsConnectionWhere>;
};

export type CommentListCommentsFieldInput = {
  connect?: InputMaybe<Array<CommentListCommentsConnectFieldInput>>;
  create?: InputMaybe<Array<CommentListCommentsCreateFieldInput>>;
};

export type CommentListCommentsNodeAggregationWhereInput = {
  AND?: InputMaybe<Array<CommentListCommentsNodeAggregationWhereInput>>;
  NOT?: InputMaybe<CommentListCommentsNodeAggregationWhereInput>;
  OR?: InputMaybe<Array<CommentListCommentsNodeAggregationWhereInput>>;
  content?: InputMaybe<StringScalarAggregationFilters>;
  createdOn?: InputMaybe<DateTimeScalarAggregationFilters>;
  deletedOn?: InputMaybe<DateTimeScalarAggregationFilters>;
  modifiedOn?: InputMaybe<DateTimeScalarAggregationFilters>;
};

export type CommentListCommentsRelationship = {
  __typename?: 'CommentListCommentsRelationship';
  cursor: Scalars['String']['output'];
  node: Comment;
};

export type CommentListCommentsUpdateConnectionInput = {
  node?: InputMaybe<CommentUpdateInput>;
  where?: InputMaybe<CommentListCommentsConnectionWhere>;
};

export type CommentListCommentsUpdateFieldInput = {
  connect?: InputMaybe<Array<CommentListCommentsConnectFieldInput>>;
  create?: InputMaybe<Array<CommentListCommentsCreateFieldInput>>;
  delete?: InputMaybe<Array<CommentListCommentsDeleteFieldInput>>;
  disconnect?: InputMaybe<Array<CommentListCommentsDisconnectFieldInput>>;
  update?: InputMaybe<CommentListCommentsUpdateConnectionInput>;
};

export type CommentListCreateInput = {
  comments?: InputMaybe<CommentListCommentsFieldInput>;
  externalId: Scalars['ID']['input'];
};

export type CommentListDeleteInput = {
  comments?: InputMaybe<Array<CommentListCommentsDeleteFieldInput>>;
};

export type CommentListEdge = {
  __typename?: 'CommentListEdge';
  cursor: Scalars['String']['output'];
  node: CommentList;
};

/** Fields to sort CommentLists by. The order in which sorts are applied is not guaranteed when specifying many fields in one CommentListSort object. */
export type CommentListSort = {
  externalId?: InputMaybe<SortDirection>;
};

export type CommentListUpdateInput = {
  comments?: InputMaybe<Array<CommentListCommentsUpdateFieldInput>>;
  externalId?: InputMaybe<IdScalarMutations>;
};

export type CommentListWhere = {
  AND?: InputMaybe<Array<CommentListWhere>>;
  NOT?: InputMaybe<CommentListWhere>;
  OR?: InputMaybe<Array<CommentListWhere>>;
  comments?: InputMaybe<CommentRelationshipFilters>;
  commentsConnection?: InputMaybe<CommentListCommentsConnectionFilters>;
  externalId?: InputMaybe<IdScalarFilters>;
};

export type CommentListsConnection = {
  __typename?: 'CommentListsConnection';
  aggregate: CommentListAggregate;
  edges: Array<CommentListEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type CommentRelationshipFilters = {
  /** Filter type where all of the related Comments match this filter */
  all?: InputMaybe<CommentWhere>;
  /** Filter type where none of the related Comments match this filter */
  none?: InputMaybe<CommentWhere>;
  /** Filter type where one of the related Comments match this filter */
  single?: InputMaybe<CommentWhere>;
  /** Filter type where some of the related Comments match this filter */
  some?: InputMaybe<CommentWhere>;
};

/** Fields to sort Comments by. The order in which sorts are applied is not guaranteed when specifying many fields in one CommentSort object. */
export type CommentSort = {
  content?: InputMaybe<SortDirection>;
  createdBy?: InputMaybe<SortDirection>;
  createdOn?: InputMaybe<SortDirection>;
  deletedBy?: InputMaybe<SortDirection>;
  deletedOn?: InputMaybe<SortDirection>;
  externalId?: InputMaybe<SortDirection>;
  modifiedBy?: InputMaybe<SortDirection>;
  modifiedOn?: InputMaybe<SortDirection>;
};

export type CommentUpdateInput = {
  content?: InputMaybe<StringScalarMutations>;
  createdBy?: InputMaybe<IdScalarMutations>;
  createdOn?: InputMaybe<DateTimeScalarMutations>;
  deletedBy?: InputMaybe<IdScalarMutations>;
  deletedOn?: InputMaybe<DateTimeScalarMutations>;
  externalId?: InputMaybe<IdScalarMutations>;
  modifiedBy?: InputMaybe<IdScalarMutations>;
  modifiedOn?: InputMaybe<DateTimeScalarMutations>;
};

export type CommentWhere = {
  AND?: InputMaybe<Array<CommentWhere>>;
  NOT?: InputMaybe<CommentWhere>;
  OR?: InputMaybe<Array<CommentWhere>>;
  content?: InputMaybe<StringScalarFilters>;
  createdBy?: InputMaybe<IdScalarFilters>;
  createdOn?: InputMaybe<DateTimeScalarFilters>;
  deletedBy?: InputMaybe<IdScalarFilters>;
  deletedOn?: InputMaybe<DateTimeScalarFilters>;
  externalId?: InputMaybe<IdScalarFilters>;
  modifiedBy?: InputMaybe<IdScalarFilters>;
  modifiedOn?: InputMaybe<DateTimeScalarFilters>;
};

export type CommentsConnection = {
  __typename?: 'CommentsConnection';
  aggregate: CommentAggregate;
  edges: Array<CommentEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type ConnectionAggregationCountFilterInput = {
  edges?: InputMaybe<IntScalarFilters>;
  nodes?: InputMaybe<IntScalarFilters>;
};

export type Count = {
  __typename?: 'Count';
  nodes: Scalars['Int']['output'];
};

export type CountConnection = {
  __typename?: 'CountConnection';
  edges: Scalars['Int']['output'];
  nodes: Scalars['Int']['output'];
};

export type CreateAttachmentListsMutationResponse = {
  __typename?: 'CreateAttachmentListsMutationResponse';
  attachmentLists: Array<AttachmentList>;
  info: CreateInfo;
};

export type CreateAttachmentsMutationResponse = {
  __typename?: 'CreateAttachmentsMutationResponse';
  attachments: Array<Attachment>;
  info: CreateInfo;
};

export type CreateBoardListsMutationResponse = {
  __typename?: 'CreateBoardListsMutationResponse';
  boardLists: Array<BoardList>;
  info: CreateInfo;
};

export type CreateBoardsMutationResponse = {
  __typename?: 'CreateBoardsMutationResponse';
  boards: Array<Board>;
  info: CreateInfo;
};

export type CreateCardListsMutationResponse = {
  __typename?: 'CreateCardListsMutationResponse';
  cardLists: Array<CardList>;
  info: CreateInfo;
};

export type CreateCardsMutationResponse = {
  __typename?: 'CreateCardsMutationResponse';
  cards: Array<Card>;
  info: CreateInfo;
};

export type CreateColumnsMutationResponse = {
  __typename?: 'CreateColumnsMutationResponse';
  columns: Array<Column>;
  info: CreateInfo;
};

export type CreateCommentListsMutationResponse = {
  __typename?: 'CreateCommentListsMutationResponse';
  commentLists: Array<CommentList>;
  info: CreateInfo;
};

export type CreateCommentsMutationResponse = {
  __typename?: 'CreateCommentsMutationResponse';
  comments: Array<Comment>;
  info: CreateInfo;
};

export type CreateHistoriesMutationResponse = {
  __typename?: 'CreateHistoriesMutationResponse';
  histories: Array<History>;
  info: CreateInfo;
};

export type CreateHistoryListsMutationResponse = {
  __typename?: 'CreateHistoryListsMutationResponse';
  historyLists: Array<HistoryList>;
  info: CreateInfo;
};

/** Information about the number of nodes and relationships created during a create mutation */
export type CreateInfo = {
  __typename?: 'CreateInfo';
  nodesCreated: Scalars['Int']['output'];
  relationshipsCreated: Scalars['Int']['output'];
};

export type DateTimeAggregateSelection = {
  __typename?: 'DateTimeAggregateSelection';
  max?: Maybe<Scalars['DateTime']['output']>;
  min?: Maybe<Scalars['DateTime']['output']>;
};

/** Filters for an aggregation of an DateTime input field */
export type DateTimeScalarAggregationFilters = {
  max?: InputMaybe<DateTimeScalarFilters>;
  min?: InputMaybe<DateTimeScalarFilters>;
};

/** DateTime filters */
export type DateTimeScalarFilters = {
  eq?: InputMaybe<Scalars['DateTime']['input']>;
  gt?: InputMaybe<Scalars['DateTime']['input']>;
  gte?: InputMaybe<Scalars['DateTime']['input']>;
  in?: InputMaybe<Array<Scalars['DateTime']['input']>>;
  lt?: InputMaybe<Scalars['DateTime']['input']>;
  lte?: InputMaybe<Scalars['DateTime']['input']>;
};

/** DateTime mutations */
export type DateTimeScalarMutations = {
  set?: InputMaybe<Scalars['DateTime']['input']>;
};

/** Information about the number of nodes and relationships deleted during a delete mutation */
export type DeleteInfo = {
  __typename?: 'DeleteInfo';
  nodesDeleted: Scalars['Int']['output'];
  relationshipsDeleted: Scalars['Int']['output'];
};

export type FloatAggregateSelection = {
  __typename?: 'FloatAggregateSelection';
  average?: Maybe<Scalars['Float']['output']>;
  max?: Maybe<Scalars['Float']['output']>;
  min?: Maybe<Scalars['Float']['output']>;
  sum?: Maybe<Scalars['Float']['output']>;
};

/** Filters for an aggregation of a float field */
export type FloatScalarAggregationFilters = {
  average?: InputMaybe<FloatScalarFilters>;
  max?: InputMaybe<FloatScalarFilters>;
  min?: InputMaybe<FloatScalarFilters>;
  sum?: InputMaybe<FloatScalarFilters>;
};

/** Float filters */
export type FloatScalarFilters = {
  eq?: InputMaybe<Scalars['Float']['input']>;
  gt?: InputMaybe<Scalars['Float']['input']>;
  gte?: InputMaybe<Scalars['Float']['input']>;
  in?: InputMaybe<Array<Scalars['Float']['input']>>;
  lt?: InputMaybe<Scalars['Float']['input']>;
  lte?: InputMaybe<Scalars['Float']['input']>;
};

/** Float mutations */
export type FloatScalarMutations = {
  add?: InputMaybe<Scalars['Float']['input']>;
  divide?: InputMaybe<Scalars['Float']['input']>;
  multiply?: InputMaybe<Scalars['Float']['input']>;
  set?: InputMaybe<Scalars['Float']['input']>;
  subtract?: InputMaybe<Scalars['Float']['input']>;
};

export type HaveLifecyle = {
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
};

export type HaveLifecyleAggregate = {
  __typename?: 'HaveLifecyleAggregate';
  count: Count;
  node: HaveLifecyleAggregateNode;
};

export type HaveLifecyleAggregateNode = {
  __typename?: 'HaveLifecyleAggregateNode';
  createdOn: DateTimeAggregateSelection;
  deletedOn: DateTimeAggregateSelection;
};

export type HaveLifecyleEdge = {
  __typename?: 'HaveLifecyleEdge';
  cursor: Scalars['String']['output'];
  node: HaveLifecyle;
};

export enum HaveLifecyleImplementation {
  Board = 'Board',
  Card = 'Card',
  User = 'User',
  UserAuth = 'UserAuth'
}

/** Fields to sort HaveLifecyles by. The order in which sorts are applied is not guaranteed when specifying many fields in one HaveLifecyleSort object. */
export type HaveLifecyleSort = {
  createdBy?: InputMaybe<SortDirection>;
  createdOn?: InputMaybe<SortDirection>;
  deletedBy?: InputMaybe<SortDirection>;
  deletedOn?: InputMaybe<SortDirection>;
};

export type HaveLifecyleWhere = {
  AND?: InputMaybe<Array<HaveLifecyleWhere>>;
  NOT?: InputMaybe<HaveLifecyleWhere>;
  OR?: InputMaybe<Array<HaveLifecyleWhere>>;
  createdBy?: InputMaybe<IdScalarFilters>;
  createdOn?: InputMaybe<DateTimeScalarFilters>;
  deletedBy?: InputMaybe<IdScalarFilters>;
  deletedOn?: InputMaybe<DateTimeScalarFilters>;
  typename?: InputMaybe<Array<HaveLifecyleImplementation>>;
};

export type HaveLifecylesConnection = {
  __typename?: 'HaveLifecylesConnection';
  aggregate: HaveLifecyleAggregate;
  edges: Array<HaveLifecyleEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type HistoriesConnection = {
  __typename?: 'HistoriesConnection';
  aggregate: HistoryAggregate;
  edges: Array<HistoryEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type History = {
  __typename?: 'History';
  after?: Maybe<Scalars['String']['output']>;
  before?: Maybe<Scalars['String']['output']>;
  dateTime: Scalars['DateTime']['output'];
  externalId: Scalars['ID']['output'];
  operation: Scalars['String']['output'];
  referenceId: Scalars['ID']['output'];
};

export type HistoryAggregate = {
  __typename?: 'HistoryAggregate';
  count: Count;
  node: HistoryAggregateNode;
};

export type HistoryAggregateNode = {
  __typename?: 'HistoryAggregateNode';
  after: StringAggregateSelection;
  before: StringAggregateSelection;
  dateTime: DateTimeAggregateSelection;
  operation: StringAggregateSelection;
};

export type HistoryConnectWhere = {
  node: HistoryWhere;
};

export type HistoryCreateInput = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  dateTime: Scalars['DateTime']['input'];
  externalId: Scalars['ID']['input'];
  operation: Scalars['String']['input'];
  referenceId: Scalars['ID']['input'];
};

export type HistoryEdge = {
  __typename?: 'HistoryEdge';
  cursor: Scalars['String']['output'];
  node: History;
};

export type HistoryList = {
  __typename?: 'HistoryList';
  externalId: Scalars['ID']['output'];
  histories: Array<History>;
  historiesConnection: HistoryListHistoriesConnection;
};


export type HistoryListHistoriesArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<HistorySort>>;
  where?: InputMaybe<HistoryWhere>;
};


export type HistoryListHistoriesConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<HistoryListHistoriesConnectionSort>>;
  where?: InputMaybe<HistoryListHistoriesConnectionWhere>;
};

export type HistoryListAggregate = {
  __typename?: 'HistoryListAggregate';
  count: Count;
};

export type HistoryListCreateInput = {
  externalId: Scalars['ID']['input'];
  histories?: InputMaybe<HistoryListHistoriesFieldInput>;
};

export type HistoryListDeleteInput = {
  histories?: InputMaybe<Array<HistoryListHistoriesDeleteFieldInput>>;
};

export type HistoryListEdge = {
  __typename?: 'HistoryListEdge';
  cursor: Scalars['String']['output'];
  node: HistoryList;
};

export type HistoryListHistoriesAggregateInput = {
  AND?: InputMaybe<Array<HistoryListHistoriesAggregateInput>>;
  NOT?: InputMaybe<HistoryListHistoriesAggregateInput>;
  OR?: InputMaybe<Array<HistoryListHistoriesAggregateInput>>;
  count?: InputMaybe<IntScalarFilters>;
  count_EQ?: InputMaybe<Scalars['Int']['input']>;
  count_GT?: InputMaybe<Scalars['Int']['input']>;
  count_GTE?: InputMaybe<Scalars['Int']['input']>;
  count_LT?: InputMaybe<Scalars['Int']['input']>;
  count_LTE?: InputMaybe<Scalars['Int']['input']>;
  node?: InputMaybe<HistoryListHistoriesNodeAggregationWhereInput>;
};

export type HistoryListHistoriesConnectFieldInput = {
  where?: InputMaybe<HistoryConnectWhere>;
};

export type HistoryListHistoriesConnection = {
  __typename?: 'HistoryListHistoriesConnection';
  aggregate: HistoryListHistoryHistoriesAggregateSelection;
  edges: Array<HistoryListHistoriesRelationship>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type HistoryListHistoriesConnectionAggregateInput = {
  AND?: InputMaybe<Array<HistoryListHistoriesConnectionAggregateInput>>;
  NOT?: InputMaybe<HistoryListHistoriesConnectionAggregateInput>;
  OR?: InputMaybe<Array<HistoryListHistoriesConnectionAggregateInput>>;
  count?: InputMaybe<ConnectionAggregationCountFilterInput>;
  node?: InputMaybe<HistoryListHistoriesNodeAggregationWhereInput>;
};

export type HistoryListHistoriesConnectionFilters = {
  /** Filter HistoryLists by aggregating results on related HistoryListHistoriesConnections */
  aggregate?: InputMaybe<HistoryListHistoriesConnectionAggregateInput>;
  /** Return HistoryLists where all of the related HistoryListHistoriesConnections match this filter */
  all?: InputMaybe<HistoryListHistoriesConnectionWhere>;
  /** Return HistoryLists where none of the related HistoryListHistoriesConnections match this filter */
  none?: InputMaybe<HistoryListHistoriesConnectionWhere>;
  /** Return HistoryLists where one of the related HistoryListHistoriesConnections match this filter */
  single?: InputMaybe<HistoryListHistoriesConnectionWhere>;
  /** Return HistoryLists where some of the related HistoryListHistoriesConnections match this filter */
  some?: InputMaybe<HistoryListHistoriesConnectionWhere>;
};

export type HistoryListHistoriesConnectionSort = {
  node?: InputMaybe<HistorySort>;
};

export type HistoryListHistoriesConnectionWhere = {
  AND?: InputMaybe<Array<HistoryListHistoriesConnectionWhere>>;
  NOT?: InputMaybe<HistoryListHistoriesConnectionWhere>;
  OR?: InputMaybe<Array<HistoryListHistoriesConnectionWhere>>;
  node?: InputMaybe<HistoryWhere>;
};

export type HistoryListHistoriesCreateFieldInput = {
  node: HistoryCreateInput;
};

export type HistoryListHistoriesDeleteFieldInput = {
  where?: InputMaybe<HistoryListHistoriesConnectionWhere>;
};

export type HistoryListHistoriesDisconnectFieldInput = {
  where?: InputMaybe<HistoryListHistoriesConnectionWhere>;
};

export type HistoryListHistoriesFieldInput = {
  connect?: InputMaybe<Array<HistoryListHistoriesConnectFieldInput>>;
  create?: InputMaybe<Array<HistoryListHistoriesCreateFieldInput>>;
};

export type HistoryListHistoriesNodeAggregationWhereInput = {
  AND?: InputMaybe<Array<HistoryListHistoriesNodeAggregationWhereInput>>;
  NOT?: InputMaybe<HistoryListHistoriesNodeAggregationWhereInput>;
  OR?: InputMaybe<Array<HistoryListHistoriesNodeAggregationWhereInput>>;
  after?: InputMaybe<StringScalarAggregationFilters>;
  before?: InputMaybe<StringScalarAggregationFilters>;
  dateTime?: InputMaybe<DateTimeScalarAggregationFilters>;
  operation?: InputMaybe<StringScalarAggregationFilters>;
};

export type HistoryListHistoriesRelationship = {
  __typename?: 'HistoryListHistoriesRelationship';
  cursor: Scalars['String']['output'];
  node: History;
};

export type HistoryListHistoriesUpdateConnectionInput = {
  node?: InputMaybe<HistoryUpdateInput>;
  where?: InputMaybe<HistoryListHistoriesConnectionWhere>;
};

export type HistoryListHistoriesUpdateFieldInput = {
  connect?: InputMaybe<Array<HistoryListHistoriesConnectFieldInput>>;
  create?: InputMaybe<Array<HistoryListHistoriesCreateFieldInput>>;
  delete?: InputMaybe<Array<HistoryListHistoriesDeleteFieldInput>>;
  disconnect?: InputMaybe<Array<HistoryListHistoriesDisconnectFieldInput>>;
  update?: InputMaybe<HistoryListHistoriesUpdateConnectionInput>;
};

export type HistoryListHistoryHistoriesAggregateSelection = {
  __typename?: 'HistoryListHistoryHistoriesAggregateSelection';
  count: CountConnection;
  node?: Maybe<HistoryListHistoryHistoriesNodeAggregateSelection>;
};

export type HistoryListHistoryHistoriesNodeAggregateSelection = {
  __typename?: 'HistoryListHistoryHistoriesNodeAggregateSelection';
  after: StringAggregateSelection;
  before: StringAggregateSelection;
  dateTime: DateTimeAggregateSelection;
  operation: StringAggregateSelection;
};

/** Fields to sort HistoryLists by. The order in which sorts are applied is not guaranteed when specifying many fields in one HistoryListSort object. */
export type HistoryListSort = {
  externalId?: InputMaybe<SortDirection>;
};

export type HistoryListUpdateInput = {
  externalId?: InputMaybe<IdScalarMutations>;
  histories?: InputMaybe<Array<HistoryListHistoriesUpdateFieldInput>>;
};

export type HistoryListWhere = {
  AND?: InputMaybe<Array<HistoryListWhere>>;
  NOT?: InputMaybe<HistoryListWhere>;
  OR?: InputMaybe<Array<HistoryListWhere>>;
  externalId?: InputMaybe<IdScalarFilters>;
  histories?: InputMaybe<HistoryRelationshipFilters>;
  historiesConnection?: InputMaybe<HistoryListHistoriesConnectionFilters>;
};

export type HistoryListsConnection = {
  __typename?: 'HistoryListsConnection';
  aggregate: HistoryListAggregate;
  edges: Array<HistoryListEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type HistoryRelationshipFilters = {
  /** Filter type where all of the related Histories match this filter */
  all?: InputMaybe<HistoryWhere>;
  /** Filter type where none of the related Histories match this filter */
  none?: InputMaybe<HistoryWhere>;
  /** Filter type where one of the related Histories match this filter */
  single?: InputMaybe<HistoryWhere>;
  /** Filter type where some of the related Histories match this filter */
  some?: InputMaybe<HistoryWhere>;
};

/** Fields to sort Histories by. The order in which sorts are applied is not guaranteed when specifying many fields in one HistorySort object. */
export type HistorySort = {
  after?: InputMaybe<SortDirection>;
  before?: InputMaybe<SortDirection>;
  dateTime?: InputMaybe<SortDirection>;
  externalId?: InputMaybe<SortDirection>;
  operation?: InputMaybe<SortDirection>;
  referenceId?: InputMaybe<SortDirection>;
};

export type HistoryUpdateInput = {
  after?: InputMaybe<StringScalarMutations>;
  before?: InputMaybe<StringScalarMutations>;
  dateTime?: InputMaybe<DateTimeScalarMutations>;
  externalId?: InputMaybe<IdScalarMutations>;
  operation?: InputMaybe<StringScalarMutations>;
  referenceId?: InputMaybe<IdScalarMutations>;
};

export type HistoryWhere = {
  AND?: InputMaybe<Array<HistoryWhere>>;
  NOT?: InputMaybe<HistoryWhere>;
  OR?: InputMaybe<Array<HistoryWhere>>;
  after?: InputMaybe<StringScalarFilters>;
  before?: InputMaybe<StringScalarFilters>;
  dateTime?: InputMaybe<DateTimeScalarFilters>;
  externalId?: InputMaybe<IdScalarFilters>;
  operation?: InputMaybe<StringScalarFilters>;
  referenceId?: InputMaybe<IdScalarFilters>;
};

/** ID list filters */
export type IdListFilters = {
  eq?: InputMaybe<Array<Scalars['ID']['input']>>;
  includes?: InputMaybe<Scalars['ID']['input']>;
};

/** ID filters */
export type IdScalarFilters = {
  contains?: InputMaybe<Scalars['ID']['input']>;
  endsWith?: InputMaybe<Scalars['ID']['input']>;
  eq?: InputMaybe<Scalars['ID']['input']>;
  in?: InputMaybe<Array<Scalars['ID']['input']>>;
  startsWith?: InputMaybe<Scalars['ID']['input']>;
};

/** ID mutations */
export type IdScalarMutations = {
  set?: InputMaybe<Scalars['ID']['input']>;
};

export type IntAggregateSelection = {
  __typename?: 'IntAggregateSelection';
  average?: Maybe<Scalars['Float']['output']>;
  max?: Maybe<Scalars['Int']['output']>;
  min?: Maybe<Scalars['Int']['output']>;
  sum?: Maybe<Scalars['Int']['output']>;
};

/** Filters for an aggregation of an int field */
export type IntScalarAggregationFilters = {
  average?: InputMaybe<FloatScalarFilters>;
  max?: InputMaybe<IntScalarFilters>;
  min?: InputMaybe<IntScalarFilters>;
  sum?: InputMaybe<IntScalarFilters>;
};

/** Int filters */
export type IntScalarFilters = {
  eq?: InputMaybe<Scalars['Int']['input']>;
  gt?: InputMaybe<Scalars['Int']['input']>;
  gte?: InputMaybe<Scalars['Int']['input']>;
  in?: InputMaybe<Array<Scalars['Int']['input']>>;
  lt?: InputMaybe<Scalars['Int']['input']>;
  lte?: InputMaybe<Scalars['Int']['input']>;
};

/** Int mutations */
export type IntScalarMutations = {
  add?: InputMaybe<Scalars['Int']['input']>;
  set?: InputMaybe<Scalars['Int']['input']>;
  subtract?: InputMaybe<Scalars['Int']['input']>;
};

/** Mutations for a list for ID */
export type ListIdMutations = {
  pop?: InputMaybe<Scalars['Int']['input']>;
  push?: InputMaybe<Array<Scalars['ID']['input']>>;
  set?: InputMaybe<Array<Scalars['ID']['input']>>;
};

/** Mutations for a list for String */
export type ListStringMutations = {
  pop?: InputMaybe<Scalars['Int']['input']>;
  push?: InputMaybe<Array<Scalars['String']['input']>>;
  set?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type Mutation = {
  __typename?: 'Mutation';
  createAttachmentLists: CreateAttachmentListsMutationResponse;
  createAttachments: CreateAttachmentsMutationResponse;
  createBoardLists: CreateBoardListsMutationResponse;
  createBoards: CreateBoardsMutationResponse;
  createCardLists: CreateCardListsMutationResponse;
  createCards: CreateCardsMutationResponse;
  createColumns: CreateColumnsMutationResponse;
  createCommentLists: CreateCommentListsMutationResponse;
  createComments: CreateCommentsMutationResponse;
  createHistories: CreateHistoriesMutationResponse;
  createHistoryLists: CreateHistoryListsMutationResponse;
  createOrUpdateGoogleUser?: Maybe<AuthPayload>;
  deleteAttachmentLists: DeleteInfo;
  deleteAttachments: DeleteInfo;
  deleteBoardLists: DeleteInfo;
  deleteBoards: DeleteInfo;
  deleteCardLists: DeleteInfo;
  deleteCards: DeleteInfo;
  deleteColumns: DeleteInfo;
  deleteCommentLists: DeleteInfo;
  deleteComments: DeleteInfo;
  deleteHistories: DeleteInfo;
  deleteHistoryLists: DeleteInfo;
  signIn?: Maybe<AuthPayload>;
  signUp?: Maybe<AuthPayload>;
  updateAttachmentLists: UpdateAttachmentListsMutationResponse;
  updateAttachments: UpdateAttachmentsMutationResponse;
  updateBoardLists: UpdateBoardListsMutationResponse;
  updateBoards: UpdateBoardsMutationResponse;
  updateCardLists: UpdateCardListsMutationResponse;
  updateCards: UpdateCardsMutationResponse;
  updateColumns: UpdateColumnsMutationResponse;
  updateCommentLists: UpdateCommentListsMutationResponse;
  updateComments: UpdateCommentsMutationResponse;
  updateHistories: UpdateHistoriesMutationResponse;
  updateHistoryLists: UpdateHistoryListsMutationResponse;
};


export type MutationCreateAttachmentListsArgs = {
  input: Array<AttachmentListCreateInput>;
};


export type MutationCreateAttachmentsArgs = {
  input: Array<AttachmentCreateInput>;
};


export type MutationCreateBoardListsArgs = {
  input: Array<BoardListCreateInput>;
};


export type MutationCreateBoardsArgs = {
  input: Array<BoardCreateInput>;
};


export type MutationCreateCardListsArgs = {
  input: Array<CardListCreateInput>;
};


export type MutationCreateCardsArgs = {
  input: Array<CardCreateInput>;
};


export type MutationCreateColumnsArgs = {
  input: Array<ColumnCreateInput>;
};


export type MutationCreateCommentListsArgs = {
  input: Array<CommentListCreateInput>;
};


export type MutationCreateCommentsArgs = {
  input: Array<CommentCreateInput>;
};


export type MutationCreateHistoriesArgs = {
  input: Array<HistoryCreateInput>;
};


export type MutationCreateHistoryListsArgs = {
  input: Array<HistoryListCreateInput>;
};


export type MutationCreateOrUpdateGoogleUserArgs = {
  email: Scalars['String']['input'];
  name: Scalars['String']['input'];
};


export type MutationDeleteAttachmentListsArgs = {
  delete?: InputMaybe<AttachmentListDeleteInput>;
  where?: InputMaybe<AttachmentListWhere>;
};


export type MutationDeleteAttachmentsArgs = {
  where?: InputMaybe<AttachmentWhere>;
};


export type MutationDeleteBoardListsArgs = {
  delete?: InputMaybe<BoardListDeleteInput>;
  where?: InputMaybe<BoardListWhere>;
};


export type MutationDeleteBoardsArgs = {
  delete?: InputMaybe<BoardDeleteInput>;
  where?: InputMaybe<BoardWhere>;
};


export type MutationDeleteCardListsArgs = {
  delete?: InputMaybe<CardListDeleteInput>;
  where?: InputMaybe<CardListWhere>;
};


export type MutationDeleteCardsArgs = {
  delete?: InputMaybe<CardDeleteInput>;
  where?: InputMaybe<CardWhere>;
};


export type MutationDeleteColumnsArgs = {
  delete?: InputMaybe<ColumnDeleteInput>;
  where?: InputMaybe<ColumnWhere>;
};


export type MutationDeleteCommentListsArgs = {
  delete?: InputMaybe<CommentListDeleteInput>;
  where?: InputMaybe<CommentListWhere>;
};


export type MutationDeleteCommentsArgs = {
  where?: InputMaybe<CommentWhere>;
};


export type MutationDeleteHistoriesArgs = {
  where?: InputMaybe<HistoryWhere>;
};


export type MutationDeleteHistoryListsArgs = {
  delete?: InputMaybe<HistoryListDeleteInput>;
  where?: InputMaybe<HistoryListWhere>;
};


export type MutationSignInArgs = {
  input: SignInInput;
};


export type MutationSignUpArgs = {
  input: SignUpInput;
};


export type MutationUpdateAttachmentListsArgs = {
  update?: InputMaybe<AttachmentListUpdateInput>;
  where?: InputMaybe<AttachmentListWhere>;
};


export type MutationUpdateAttachmentsArgs = {
  update?: InputMaybe<AttachmentUpdateInput>;
  where?: InputMaybe<AttachmentWhere>;
};


export type MutationUpdateBoardListsArgs = {
  update?: InputMaybe<BoardListUpdateInput>;
  where?: InputMaybe<BoardListWhere>;
};


export type MutationUpdateBoardsArgs = {
  update?: InputMaybe<BoardUpdateInput>;
  where?: InputMaybe<BoardWhere>;
};


export type MutationUpdateCardListsArgs = {
  update?: InputMaybe<CardListUpdateInput>;
  where?: InputMaybe<CardListWhere>;
};


export type MutationUpdateCardsArgs = {
  update?: InputMaybe<CardUpdateInput>;
  where?: InputMaybe<CardWhere>;
};


export type MutationUpdateColumnsArgs = {
  update?: InputMaybe<ColumnUpdateInput>;
  where?: InputMaybe<ColumnWhere>;
};


export type MutationUpdateCommentListsArgs = {
  update?: InputMaybe<CommentListUpdateInput>;
  where?: InputMaybe<CommentListWhere>;
};


export type MutationUpdateCommentsArgs = {
  update?: InputMaybe<CommentUpdateInput>;
  where?: InputMaybe<CommentWhere>;
};


export type MutationUpdateHistoriesArgs = {
  update?: InputMaybe<HistoryUpdateInput>;
  where?: InputMaybe<HistoryWhere>;
};


export type MutationUpdateHistoryListsArgs = {
  update?: InputMaybe<HistoryListUpdateInput>;
  where?: InputMaybe<HistoryListWhere>;
};

/** Pagination information (Relay) */
export type PageInfo = {
  __typename?: 'PageInfo';
  endCursor?: Maybe<Scalars['String']['output']>;
  hasNextPage: Scalars['Boolean']['output'];
  hasPreviousPage: Scalars['Boolean']['output'];
  startCursor?: Maybe<Scalars['String']['output']>;
};

export type Query = {
  __typename?: 'Query';
  attachmentLists: Array<AttachmentList>;
  attachmentListsConnection: AttachmentListsConnection;
  attachments: Array<Attachment>;
  attachmentsConnection: AttachmentsConnection;
  boardLists: Array<BoardList>;
  boardListsConnection: BoardListsConnection;
  boards: Array<Board>;
  boardsConnection: BoardsConnection;
  cardLists: Array<CardList>;
  cardListsConnection: CardListsConnection;
  cards: Array<Card>;
  cardsConnection: CardsConnection;
  columns: Array<Column>;
  columnsConnection: ColumnsConnection;
  commentLists: Array<CommentList>;
  commentListsConnection: CommentListsConnection;
  comments: Array<Comment>;
  commentsConnection: CommentsConnection;
  haveLifecyles: Array<HaveLifecyle>;
  haveLifecylesConnection: HaveLifecylesConnection;
  hello?: Maybe<Scalars['String']['output']>;
  histories: Array<History>;
  historiesConnection: HistoriesConnection;
  historyLists: Array<HistoryList>;
  historyListsConnection: HistoryListsConnection;
  me?: Maybe<User>;
  trackables: Array<Trackable>;
  trackablesConnection: TrackablesConnection;
  uploadables: Array<Uploadable>;
  uploadablesConnection: UploadablesConnection;
};


export type QueryAttachmentListsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<AttachmentListSort>>;
  where?: InputMaybe<AttachmentListWhere>;
};


export type QueryAttachmentListsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<AttachmentListSort>>;
  where?: InputMaybe<AttachmentListWhere>;
};


export type QueryAttachmentsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<AttachmentSort>>;
  where?: InputMaybe<AttachmentWhere>;
};


export type QueryAttachmentsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<AttachmentSort>>;
  where?: InputMaybe<AttachmentWhere>;
};


export type QueryBoardListsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<BoardListSort>>;
  where?: InputMaybe<BoardListWhere>;
};


export type QueryBoardListsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<BoardListSort>>;
  where?: InputMaybe<BoardListWhere>;
};


export type QueryBoardsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<BoardSort>>;
  where?: InputMaybe<BoardWhere>;
};


export type QueryBoardsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<BoardSort>>;
  where?: InputMaybe<BoardWhere>;
};


export type QueryCardListsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<CardListSort>>;
  where?: InputMaybe<CardListWhere>;
};


export type QueryCardListsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<CardListSort>>;
  where?: InputMaybe<CardListWhere>;
};


export type QueryCardsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<CardSort>>;
  where?: InputMaybe<CardWhere>;
};


export type QueryCardsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<CardSort>>;
  where?: InputMaybe<CardWhere>;
};


export type QueryColumnsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<ColumnSort>>;
  where?: InputMaybe<ColumnWhere>;
};


export type QueryColumnsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<ColumnSort>>;
  where?: InputMaybe<ColumnWhere>;
};


export type QueryCommentListsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<CommentListSort>>;
  where?: InputMaybe<CommentListWhere>;
};


export type QueryCommentListsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<CommentListSort>>;
  where?: InputMaybe<CommentListWhere>;
};


export type QueryCommentsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<CommentSort>>;
  where?: InputMaybe<CommentWhere>;
};


export type QueryCommentsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<CommentSort>>;
  where?: InputMaybe<CommentWhere>;
};


export type QueryHaveLifecylesArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<HaveLifecyleSort>>;
  where?: InputMaybe<HaveLifecyleWhere>;
};


export type QueryHaveLifecylesConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<HaveLifecyleSort>>;
  where?: InputMaybe<HaveLifecyleWhere>;
};


export type QueryHistoriesArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<HistorySort>>;
  where?: InputMaybe<HistoryWhere>;
};


export type QueryHistoriesConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<HistorySort>>;
  where?: InputMaybe<HistoryWhere>;
};


export type QueryHistoryListsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<HistoryListSort>>;
  where?: InputMaybe<HistoryListWhere>;
};


export type QueryHistoryListsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<HistoryListSort>>;
  where?: InputMaybe<HistoryListWhere>;
};


export type QueryTrackablesArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<TrackableSort>>;
  where?: InputMaybe<TrackableWhere>;
};


export type QueryTrackablesConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<TrackableSort>>;
  where?: InputMaybe<TrackableWhere>;
};


export type QueryUploadablesArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<UploadableSort>>;
  where?: InputMaybe<UploadableWhere>;
};


export type QueryUploadablesConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<UploadableSort>>;
  where?: InputMaybe<UploadableWhere>;
};

export type SignInInput = {
  email: Scalars['String']['input'];
  password: Scalars['String']['input'];
};

export type SignUpInput = {
  email: Scalars['String']['input'];
  name: Scalars['String']['input'];
  password: Scalars['String']['input'];
};

/** An enum for sorting in either ascending or descending order. */
export enum SortDirection {
  /** Sort by field values in ascending order. */
  Asc = 'ASC',
  /** Sort by field values in descending order. */
  Desc = 'DESC'
}

export type StringAggregateSelection = {
  __typename?: 'StringAggregateSelection';
  longest?: Maybe<Scalars['String']['output']>;
  shortest?: Maybe<Scalars['String']['output']>;
};

/** String list filters */
export type StringListFilters = {
  eq?: InputMaybe<Array<Scalars['String']['input']>>;
  includes?: InputMaybe<Scalars['String']['input']>;
};

/** Filters for an aggregation of a string field */
export type StringScalarAggregationFilters = {
  averageLength?: InputMaybe<FloatScalarFilters>;
  longestLength?: InputMaybe<IntScalarFilters>;
  shortestLength?: InputMaybe<IntScalarFilters>;
};

/** String filters */
export type StringScalarFilters = {
  contains?: InputMaybe<Scalars['String']['input']>;
  endsWith?: InputMaybe<Scalars['String']['input']>;
  eq?: InputMaybe<Scalars['String']['input']>;
  in?: InputMaybe<Array<Scalars['String']['input']>>;
  startsWith?: InputMaybe<Scalars['String']['input']>;
};

/** String mutations */
export type StringScalarMutations = {
  set?: InputMaybe<Scalars['String']['input']>;
};

export type Trackable = {
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
  modifiedBy?: Maybe<Scalars['ID']['output']>;
  modifiedOn?: Maybe<Scalars['DateTime']['output']>;
};

export type TrackableAggregate = {
  __typename?: 'TrackableAggregate';
  count: Count;
  node: TrackableAggregateNode;
};

export type TrackableAggregateNode = {
  __typename?: 'TrackableAggregateNode';
  createdOn: DateTimeAggregateSelection;
  deletedOn: DateTimeAggregateSelection;
  modifiedOn: DateTimeAggregateSelection;
};

export type TrackableEdge = {
  __typename?: 'TrackableEdge';
  cursor: Scalars['String']['output'];
  node: Trackable;
};

export enum TrackableImplementation {
  Comment = 'Comment'
}

/** Fields to sort Trackables by. The order in which sorts are applied is not guaranteed when specifying many fields in one TrackableSort object. */
export type TrackableSort = {
  createdBy?: InputMaybe<SortDirection>;
  createdOn?: InputMaybe<SortDirection>;
  deletedBy?: InputMaybe<SortDirection>;
  deletedOn?: InputMaybe<SortDirection>;
  modifiedBy?: InputMaybe<SortDirection>;
  modifiedOn?: InputMaybe<SortDirection>;
};

export type TrackableWhere = {
  AND?: InputMaybe<Array<TrackableWhere>>;
  NOT?: InputMaybe<TrackableWhere>;
  OR?: InputMaybe<Array<TrackableWhere>>;
  createdBy?: InputMaybe<IdScalarFilters>;
  createdOn?: InputMaybe<DateTimeScalarFilters>;
  deletedBy?: InputMaybe<IdScalarFilters>;
  deletedOn?: InputMaybe<DateTimeScalarFilters>;
  modifiedBy?: InputMaybe<IdScalarFilters>;
  modifiedOn?: InputMaybe<DateTimeScalarFilters>;
  typename?: InputMaybe<Array<TrackableImplementation>>;
};

export type TrackablesConnection = {
  __typename?: 'TrackablesConnection';
  aggregate: TrackableAggregate;
  edges: Array<TrackableEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type UpdateAttachmentListsMutationResponse = {
  __typename?: 'UpdateAttachmentListsMutationResponse';
  attachmentLists: Array<AttachmentList>;
  info: UpdateInfo;
};

export type UpdateAttachmentsMutationResponse = {
  __typename?: 'UpdateAttachmentsMutationResponse';
  attachments: Array<Attachment>;
  info: UpdateInfo;
};

export type UpdateBoardListsMutationResponse = {
  __typename?: 'UpdateBoardListsMutationResponse';
  boardLists: Array<BoardList>;
  info: UpdateInfo;
};

export type UpdateBoardsMutationResponse = {
  __typename?: 'UpdateBoardsMutationResponse';
  boards: Array<Board>;
  info: UpdateInfo;
};

export type UpdateCardListsMutationResponse = {
  __typename?: 'UpdateCardListsMutationResponse';
  cardLists: Array<CardList>;
  info: UpdateInfo;
};

export type UpdateCardsMutationResponse = {
  __typename?: 'UpdateCardsMutationResponse';
  cards: Array<Card>;
  info: UpdateInfo;
};

export type UpdateColumnsMutationResponse = {
  __typename?: 'UpdateColumnsMutationResponse';
  columns: Array<Column>;
  info: UpdateInfo;
};

export type UpdateCommentListsMutationResponse = {
  __typename?: 'UpdateCommentListsMutationResponse';
  commentLists: Array<CommentList>;
  info: UpdateInfo;
};

export type UpdateCommentsMutationResponse = {
  __typename?: 'UpdateCommentsMutationResponse';
  comments: Array<Comment>;
  info: UpdateInfo;
};

export type UpdateHistoriesMutationResponse = {
  __typename?: 'UpdateHistoriesMutationResponse';
  histories: Array<History>;
  info: UpdateInfo;
};

export type UpdateHistoryListsMutationResponse = {
  __typename?: 'UpdateHistoryListsMutationResponse';
  historyLists: Array<HistoryList>;
  info: UpdateInfo;
};

/** Information about the number of nodes and relationships created and deleted during an update mutation */
export type UpdateInfo = {
  __typename?: 'UpdateInfo';
  nodesCreated: Scalars['Int']['output'];
  nodesDeleted: Scalars['Int']['output'];
  relationshipsCreated: Scalars['Int']['output'];
  relationshipsDeleted: Scalars['Int']['output'];
};

export type Uploadable = {
  uploadedBy: Scalars['ID']['output'];
  uploadedOn: Scalars['DateTime']['output'];
};

export type UploadableAggregate = {
  __typename?: 'UploadableAggregate';
  count: Count;
  node: UploadableAggregateNode;
};

export type UploadableAggregateNode = {
  __typename?: 'UploadableAggregateNode';
  uploadedOn: DateTimeAggregateSelection;
};

export type UploadableEdge = {
  __typename?: 'UploadableEdge';
  cursor: Scalars['String']['output'];
  node: Uploadable;
};

export enum UploadableImplementation {
  Attachment = 'Attachment'
}

/** Fields to sort Uploadables by. The order in which sorts are applied is not guaranteed when specifying many fields in one UploadableSort object. */
export type UploadableSort = {
  uploadedBy?: InputMaybe<SortDirection>;
  uploadedOn?: InputMaybe<SortDirection>;
};

export type UploadableWhere = {
  AND?: InputMaybe<Array<UploadableWhere>>;
  NOT?: InputMaybe<UploadableWhere>;
  OR?: InputMaybe<Array<UploadableWhere>>;
  typename?: InputMaybe<Array<UploadableImplementation>>;
  uploadedBy?: InputMaybe<IdScalarFilters>;
  uploadedOn?: InputMaybe<DateTimeScalarFilters>;
};

export type UploadablesConnection = {
  __typename?: 'UploadablesConnection';
  aggregate: UploadableAggregate;
  edges: Array<UploadableEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type User = HaveLifecyle & {
  __typename?: 'User';
  assignedCards: CardList;
  authMethods: Array<UserAuth>;
  authMethodsConnection: UserAuthMethodsConnection;
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
  email: Scalars['String']['output'];
  externalId: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  ownedBoards: BoardList;
};


export type UserAuthMethodsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<UserAuthSort>>;
  where?: InputMaybe<UserAuthWhere>;
};


export type UserAuthMethodsConnectionArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<Array<UserAuthMethodsConnectionSort>>;
  where?: InputMaybe<UserAuthMethodsConnectionWhere>;
};

export type UserAuth = HaveLifecyle & {
  __typename?: 'UserAuth';
  createdBy: Scalars['ID']['output'];
  createdOn: Scalars['DateTime']['output'];
  deletedBy?: Maybe<Scalars['ID']['output']>;
  deletedOn?: Maybe<Scalars['DateTime']['output']>;
  email: Scalars['String']['output'];
  externalId: Scalars['ID']['output'];
  password?: Maybe<Scalars['String']['output']>;
  provider: AuthProvider;
  providerUserId: Scalars['String']['output'];
  user: User;
};

export type UserAuthMethodsConnection = {
  __typename?: 'UserAuthMethodsConnection';
  aggregate: UserUserAuthAuthMethodsAggregateSelection;
  edges: Array<UserAuthMethodsRelationship>;
  pageInfo: PageInfo;
  totalCount: Scalars['Int']['output'];
};

export type UserAuthMethodsConnectionSort = {
  node?: InputMaybe<UserAuthSort>;
};

export type UserAuthMethodsConnectionWhere = {
  AND?: InputMaybe<Array<UserAuthMethodsConnectionWhere>>;
  NOT?: InputMaybe<UserAuthMethodsConnectionWhere>;
  OR?: InputMaybe<Array<UserAuthMethodsConnectionWhere>>;
  node?: InputMaybe<UserAuthWhere>;
};

export type UserAuthMethodsRelationship = {
  __typename?: 'UserAuthMethodsRelationship';
  cursor: Scalars['String']['output'];
  node: UserAuth;
};

/** Fields to sort UserAuths by. The order in which sorts are applied is not guaranteed when specifying many fields in one UserAuthSort object. */
export type UserAuthSort = {
  createdBy?: InputMaybe<SortDirection>;
  createdOn?: InputMaybe<SortDirection>;
  deletedBy?: InputMaybe<SortDirection>;
  deletedOn?: InputMaybe<SortDirection>;
  email?: InputMaybe<SortDirection>;
  externalId?: InputMaybe<SortDirection>;
  password?: InputMaybe<SortDirection>;
  provider?: InputMaybe<SortDirection>;
  providerUserId?: InputMaybe<SortDirection>;
};

export type UserAuthWhere = {
  AND?: InputMaybe<Array<UserAuthWhere>>;
  NOT?: InputMaybe<UserAuthWhere>;
  OR?: InputMaybe<Array<UserAuthWhere>>;
  createdBy?: InputMaybe<IdScalarFilters>;
  createdOn?: InputMaybe<DateTimeScalarFilters>;
  deletedBy?: InputMaybe<IdScalarFilters>;
  deletedOn?: InputMaybe<DateTimeScalarFilters>;
  email?: InputMaybe<StringScalarFilters>;
  externalId?: InputMaybe<IdScalarFilters>;
  password?: InputMaybe<StringScalarFilters>;
  provider?: InputMaybe<AuthProviderEnumScalarFilters>;
  providerUserId?: InputMaybe<StringScalarFilters>;
};

export type UserUserAuthAuthMethodsAggregateSelection = {
  __typename?: 'UserUserAuthAuthMethodsAggregateSelection';
  count: CountConnection;
  node?: Maybe<UserUserAuthAuthMethodsNodeAggregateSelection>;
};

export type UserUserAuthAuthMethodsNodeAggregateSelection = {
  __typename?: 'UserUserAuthAuthMethodsNodeAggregateSelection';
  createdOn: DateTimeAggregateSelection;
  deletedOn: DateTimeAggregateSelection;
  email: StringAggregateSelection;
  password: StringAggregateSelection;
  providerUserId: StringAggregateSelection;
};

export type CreateOrUpdateGoogleUserMutationVariables = Exact<{
  email: Scalars['String']['input'];
  name: Scalars['String']['input'];
}>;


export type CreateOrUpdateGoogleUserMutation = { __typename?: 'Mutation', createOrUpdateGoogleUser?: { __typename?: 'AuthPayload', token: string, user: { __typename?: 'User', externalId: string, email: string, name: string } } | null };

export type SignUpMutationVariables = Exact<{
  input: SignUpInput;
}>;


export type SignUpMutation = { __typename?: 'Mutation', signUp?: { __typename?: 'AuthPayload', token: string, user: { __typename?: 'User', externalId: string, email: string, name: string } } | null };

export type SignInMutationVariables = Exact<{
  input: SignInInput;
}>;


export type SignInMutation = { __typename?: 'Mutation', signIn?: { __typename?: 'AuthPayload', token: string, user: { __typename?: 'User', externalId: string, email: string, name: string } } | null };

export type GetBoardWithColumnsQueryVariables = Exact<{
  boardId: Scalars['ID']['input'];
}>;


export type GetBoardWithColumnsQuery = { __typename?: 'Query', boards: Array<{ __typename?: 'Board', externalId: string, name: string, columnOrder: Array<string>, columns: Array<{ __typename?: 'Column', externalId: string, name: string, type: ColumnType, wipLimit?: number | null, cardPriorityOrder: Array<string>, cards: Array<{ __typename?: 'Card', externalId: string, title: string, description?: string | null, tags: Array<string>, color?: string | null, priority?: number | null, estimatedHours?: number | null, workedHours?: number | null, dueDate?: string | null, createdBy: string, createdOn: string, assignee?: { __typename?: 'User', externalId: string, name: string, email: string } | null }> }> }>, me?: { __typename?: 'User', externalId: string, email: string, name: string } | null };

export type CreateCardMutationVariables = Exact<{
  columnId: Scalars['ID']['input'];
  cardInput: CardCreateInput;
  updatedCardPriorityOrder: Array<Scalars['ID']['input']> | Scalars['ID']['input'];
}>;


export type CreateCardMutation = { __typename?: 'Mutation', updateColumns: { __typename?: 'UpdateColumnsMutationResponse', info: { __typename?: 'UpdateInfo', nodesCreated: number, nodesDeleted: number }, columns: Array<{ __typename?: 'Column', externalId: string, cardPriorityOrder: Array<string>, cards: Array<{ __typename?: 'Card', externalId: string, title: string, description?: string | null, tags: Array<string>, color?: string | null, priority?: number | null, estimatedHours?: number | null, workedHours?: number | null, dueDate?: string | null, createdBy: string, createdOn: string, assignee?: { __typename?: 'User', externalId: string, name: string, email: string } | null }> }> } };

export type MoveCardMutationVariables = Exact<{
  cardId: Scalars['ID']['input'];
  newColumnId: Scalars['ID']['input'];
  oldColumnId: Scalars['ID']['input'];
  newColumnCardOrder: Array<Scalars['ID']['input']> | Scalars['ID']['input'];
  oldColumnCardOrder: Array<Scalars['ID']['input']> | Scalars['ID']['input'];
}>;


export type MoveCardMutation = { __typename?: 'Mutation', updateOldColumn: { __typename?: 'UpdateColumnsMutationResponse', columns: Array<{ __typename?: 'Column', externalId: string, cardPriorityOrder: Array<string> }> }, updateNewColumn: { __typename?: 'UpdateColumnsMutationResponse', columns: Array<{ __typename?: 'Column', externalId: string, cardPriorityOrder: Array<string> }> } };

export type ReorderCardsInColumnMutationVariables = Exact<{
  columnId: Scalars['ID']['input'];
  cardPriorityOrder: Array<Scalars['ID']['input']> | Scalars['ID']['input'];
}>;


export type ReorderCardsInColumnMutation = { __typename?: 'Mutation', updateColumns: { __typename?: 'UpdateColumnsMutationResponse', info: { __typename?: 'UpdateInfo', nodesCreated: number, nodesDeleted: number }, columns: Array<{ __typename?: 'Column', externalId: string, cardPriorityOrder: Array<string> }> } };

export type GetCardWithDetailsQueryVariables = Exact<{
  cardId: Scalars['ID']['input'];
}>;


export type GetCardWithDetailsQuery = { __typename?: 'Query', columns: Array<{ __typename?: 'Column', externalId: string, name: string, type: ColumnType, cards: Array<{ __typename?: 'Card', externalId: string, title: string, description?: string | null, tags: Array<string>, boardTags: Array<string>, estimatedHours?: number | null, workedHours?: number | null, color?: string | null, dueDate?: string | null, priority?: number | null, createdBy: string, createdOn: string, deletedBy?: string | null, deletedOn?: string | null, assignee?: { __typename?: 'User', externalId: string, name: string, email: string } | null, commentList?: { __typename?: 'CommentList', externalId: string, comments: Array<{ __typename?: 'Comment', externalId: string, content: string, createdBy: string, createdOn: string, modifiedBy?: string | null, modifiedOn?: string | null, deletedBy?: string | null, deletedOn?: string | null, attachmentList?: { __typename?: 'AttachmentList', externalId: string, attachments: Array<{ __typename?: 'Attachment', externalId: string, name: string, url: string, uploadedBy: string, uploadedOn: string }> } | null, commentList: { __typename?: 'CommentList', externalId: string, comments: Array<{ __typename?: 'Comment', externalId: string, content: string, createdBy: string, createdOn: string, modifiedBy?: string | null, modifiedOn?: string | null, deletedBy?: string | null, deletedOn?: string | null }> } }> } | null, attachmentList?: { __typename?: 'AttachmentList', externalId: string, attachments: Array<{ __typename?: 'Attachment', externalId: string, name: string, url: string, uploadedBy: string, uploadedOn: string }> } | null, historyList?: { __typename?: 'HistoryList', externalId: string, histories: Array<{ __typename?: 'History', externalId: string, referenceId: string, operation: string, dateTime: string, before?: string | null, after?: string | null }> } | null }> }> };

export type AddCommentMutationVariables = Exact<{
  commentInput: CommentCreateInput;
}>;


export type AddCommentMutation = { __typename?: 'Mutation', createComments: { __typename?: 'CreateCommentsMutationResponse', info: { __typename?: 'CreateInfo', nodesCreated: number }, comments: Array<{ __typename?: 'Comment', externalId: string, content: string, createdBy: string, createdOn: string, modifiedBy?: string | null, modifiedOn?: string | null, deletedBy?: string | null, deletedOn?: string | null, attachmentList?: { __typename?: 'AttachmentList', externalId: string, attachments: Array<{ __typename?: 'Attachment', externalId: string, name: string, url: string, uploadedBy: string, uploadedOn: string }> } | null, commentList: { __typename?: 'CommentList', externalId: string, comments: Array<{ __typename?: 'Comment', externalId: string, content: string, createdBy: string, createdOn: string, modifiedBy?: string | null, modifiedOn?: string | null, deletedBy?: string | null, deletedOn?: string | null }> } }> } };

export type AddAttachmentMutationVariables = Exact<{
  attachmentInput: AttachmentCreateInput;
}>;


export type AddAttachmentMutation = { __typename?: 'Mutation', createAttachments: { __typename?: 'CreateAttachmentsMutationResponse', info: { __typename?: 'CreateInfo', nodesCreated: number }, attachments: Array<{ __typename?: 'Attachment', externalId: string, name: string, url: string, uploadedBy: string, uploadedOn: string }> } };

export type GetMeAndBoardsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetMeAndBoardsQuery = { __typename?: 'Query', me?: { __typename?: 'User', externalId: string, email: string, name: string } | null, boards: Array<{ __typename?: 'Board', externalId: string, name: string, createdOn: string }> };

export type CreateBoardMutationVariables = Exact<{
  input: Array<BoardCreateInput> | BoardCreateInput;
}>;


export type CreateBoardMutation = { __typename?: 'Mutation', createBoards: { __typename?: 'CreateBoardsMutationResponse', info: { __typename?: 'CreateInfo', nodesCreated: number }, boards: Array<{ __typename?: 'Board', externalId: string, name: string, createdOn: string }> } };

export type CreateBoardLinkedToCardMutationVariables = Exact<{
  boardInput: BoardCreateInput;
  cardId: Scalars['ID']['input'];
  boardId: Scalars['ID']['input'];
}>;


export type CreateBoardLinkedToCardMutation = { __typename?: 'Mutation', createBoards: { __typename?: 'CreateBoardsMutationResponse', info: { __typename?: 'CreateInfo', nodesCreated: number }, boards: Array<{ __typename?: 'Board', externalId: string, name: string, createdOn: string }> }, updateCards: { __typename?: 'UpdateCardsMutationResponse', cards: Array<{ __typename?: 'Card', externalId: string, linkedBoards: Array<{ __typename?: 'Board', externalId: string, name: string }> }> } };

export type CreateColumnsMutationVariables = Exact<{
  input: Array<ColumnCreateInput> | ColumnCreateInput;
}>;


export type CreateColumnsMutation = { __typename?: 'Mutation', createColumns: { __typename?: 'CreateColumnsMutationResponse', info: { __typename?: 'CreateInfo', nodesCreated: number }, columns: Array<{ __typename?: 'Column', externalId: string, name: string, type: ColumnType }> } };


export const CreateOrUpdateGoogleUserDocument = gql`
    mutation CreateOrUpdateGoogleUser($email: String!, $name: String!) {
  createOrUpdateGoogleUser(email: $email, name: $name) {
    token
    user {
      externalId
      email
      name
    }
  }
}
    `;
export type CreateOrUpdateGoogleUserMutationFn = Apollo.MutationFunction<CreateOrUpdateGoogleUserMutation, CreateOrUpdateGoogleUserMutationVariables>;

/**
 * __useCreateOrUpdateGoogleUserMutation__
 *
 * To run a mutation, you first call `useCreateOrUpdateGoogleUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateOrUpdateGoogleUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createOrUpdateGoogleUserMutation, { data, loading, error }] = useCreateOrUpdateGoogleUserMutation({
 *   variables: {
 *      email: // value for 'email'
 *      name: // value for 'name'
 *   },
 * });
 */
export function useCreateOrUpdateGoogleUserMutation(baseOptions?: Apollo.MutationHookOptions<CreateOrUpdateGoogleUserMutation, CreateOrUpdateGoogleUserMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateOrUpdateGoogleUserMutation, CreateOrUpdateGoogleUserMutationVariables>(CreateOrUpdateGoogleUserDocument, options);
      }
export type CreateOrUpdateGoogleUserMutationHookResult = ReturnType<typeof useCreateOrUpdateGoogleUserMutation>;
export type CreateOrUpdateGoogleUserMutationResult = Apollo.MutationResult<CreateOrUpdateGoogleUserMutation>;
export type CreateOrUpdateGoogleUserMutationOptions = Apollo.BaseMutationOptions<CreateOrUpdateGoogleUserMutation, CreateOrUpdateGoogleUserMutationVariables>;
export const SignUpDocument = gql`
    mutation SignUp($input: SignUpInput!) {
  signUp(input: $input) {
    token
    user {
      externalId
      email
      name
    }
  }
}
    `;
export type SignUpMutationFn = Apollo.MutationFunction<SignUpMutation, SignUpMutationVariables>;

/**
 * __useSignUpMutation__
 *
 * To run a mutation, you first call `useSignUpMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSignUpMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [signUpMutation, { data, loading, error }] = useSignUpMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSignUpMutation(baseOptions?: Apollo.MutationHookOptions<SignUpMutation, SignUpMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<SignUpMutation, SignUpMutationVariables>(SignUpDocument, options);
      }
export type SignUpMutationHookResult = ReturnType<typeof useSignUpMutation>;
export type SignUpMutationResult = Apollo.MutationResult<SignUpMutation>;
export type SignUpMutationOptions = Apollo.BaseMutationOptions<SignUpMutation, SignUpMutationVariables>;
export const SignInDocument = gql`
    mutation SignIn($input: SignInInput!) {
  signIn(input: $input) {
    token
    user {
      externalId
      email
      name
    }
  }
}
    `;
export type SignInMutationFn = Apollo.MutationFunction<SignInMutation, SignInMutationVariables>;

/**
 * __useSignInMutation__
 *
 * To run a mutation, you first call `useSignInMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSignInMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [signInMutation, { data, loading, error }] = useSignInMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSignInMutation(baseOptions?: Apollo.MutationHookOptions<SignInMutation, SignInMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<SignInMutation, SignInMutationVariables>(SignInDocument, options);
      }
export type SignInMutationHookResult = ReturnType<typeof useSignInMutation>;
export type SignInMutationResult = Apollo.MutationResult<SignInMutation>;
export type SignInMutationOptions = Apollo.BaseMutationOptions<SignInMutation, SignInMutationVariables>;
export const GetBoardWithColumnsDocument = gql`
    query GetBoardWithColumns($boardId: ID!) {
  boards(where: {externalId: {eq: $boardId}}) {
    externalId
    name
    columnOrder
    columns {
      externalId
      name
      type
      wipLimit
      cardPriorityOrder
      cards {
        externalId
        title
        description
        tags
        color
        priority
        estimatedHours
        workedHours
        dueDate
        createdBy
        createdOn
        assignee {
          externalId
          name
          email
        }
      }
    }
  }
  me {
    externalId
    email
    name
  }
}
    `;

/**
 * __useGetBoardWithColumnsQuery__
 *
 * To run a query within a React component, call `useGetBoardWithColumnsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetBoardWithColumnsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetBoardWithColumnsQuery({
 *   variables: {
 *      boardId: // value for 'boardId'
 *   },
 * });
 */
export function useGetBoardWithColumnsQuery(baseOptions: Apollo.QueryHookOptions<GetBoardWithColumnsQuery, GetBoardWithColumnsQueryVariables> & ({ variables: GetBoardWithColumnsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetBoardWithColumnsQuery, GetBoardWithColumnsQueryVariables>(GetBoardWithColumnsDocument, options);
      }
export function useGetBoardWithColumnsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetBoardWithColumnsQuery, GetBoardWithColumnsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetBoardWithColumnsQuery, GetBoardWithColumnsQueryVariables>(GetBoardWithColumnsDocument, options);
        }
export function useGetBoardWithColumnsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetBoardWithColumnsQuery, GetBoardWithColumnsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetBoardWithColumnsQuery, GetBoardWithColumnsQueryVariables>(GetBoardWithColumnsDocument, options);
        }
export type GetBoardWithColumnsQueryHookResult = ReturnType<typeof useGetBoardWithColumnsQuery>;
export type GetBoardWithColumnsLazyQueryHookResult = ReturnType<typeof useGetBoardWithColumnsLazyQuery>;
export type GetBoardWithColumnsSuspenseQueryHookResult = ReturnType<typeof useGetBoardWithColumnsSuspenseQuery>;
export type GetBoardWithColumnsQueryResult = Apollo.QueryResult<GetBoardWithColumnsQuery, GetBoardWithColumnsQueryVariables>;
export const CreateCardDocument = gql`
    mutation CreateCard($columnId: ID!, $cardInput: CardCreateInput!, $updatedCardPriorityOrder: [ID!]!) {
  updateColumns(
    where: {externalId: {eq: $columnId}}
    update: {cards: {create: [{node: $cardInput}]}, cardPriorityOrder: {set: $updatedCardPriorityOrder}}
  ) {
    info {
      nodesCreated
      nodesDeleted
    }
    columns {
      externalId
      cardPriorityOrder
      cards {
        externalId
        title
        description
        tags
        color
        priority
        estimatedHours
        workedHours
        dueDate
        createdBy
        createdOn
        assignee {
          externalId
          name
          email
        }
      }
    }
  }
}
    `;
export type CreateCardMutationFn = Apollo.MutationFunction<CreateCardMutation, CreateCardMutationVariables>;

/**
 * __useCreateCardMutation__
 *
 * To run a mutation, you first call `useCreateCardMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateCardMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createCardMutation, { data, loading, error }] = useCreateCardMutation({
 *   variables: {
 *      columnId: // value for 'columnId'
 *      cardInput: // value for 'cardInput'
 *      updatedCardPriorityOrder: // value for 'updatedCardPriorityOrder'
 *   },
 * });
 */
export function useCreateCardMutation(baseOptions?: Apollo.MutationHookOptions<CreateCardMutation, CreateCardMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateCardMutation, CreateCardMutationVariables>(CreateCardDocument, options);
      }
export type CreateCardMutationHookResult = ReturnType<typeof useCreateCardMutation>;
export type CreateCardMutationResult = Apollo.MutationResult<CreateCardMutation>;
export type CreateCardMutationOptions = Apollo.BaseMutationOptions<CreateCardMutation, CreateCardMutationVariables>;
export const MoveCardDocument = gql`
    mutation MoveCard($cardId: ID!, $newColumnId: ID!, $oldColumnId: ID!, $newColumnCardOrder: [ID!]!, $oldColumnCardOrder: [ID!]!) {
  updateOldColumn: updateColumns(
    where: {externalId: {eq: $oldColumnId}}
    update: {cards: {disconnect: {where: {node: {externalId: {eq: $cardId}}}}}, cardPriorityOrder: {set: $oldColumnCardOrder}}
  ) {
    columns {
      externalId
      cardPriorityOrder
    }
  }
  updateNewColumn: updateColumns(
    where: {externalId: {eq: $newColumnId}}
    update: {cards: {connect: {where: {node: {externalId: {eq: $cardId}}}}}, cardPriorityOrder: {set: $newColumnCardOrder}}
  ) {
    columns {
      externalId
      cardPriorityOrder
    }
  }
}
    `;
export type MoveCardMutationFn = Apollo.MutationFunction<MoveCardMutation, MoveCardMutationVariables>;

/**
 * __useMoveCardMutation__
 *
 * To run a mutation, you first call `useMoveCardMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useMoveCardMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [moveCardMutation, { data, loading, error }] = useMoveCardMutation({
 *   variables: {
 *      cardId: // value for 'cardId'
 *      newColumnId: // value for 'newColumnId'
 *      oldColumnId: // value for 'oldColumnId'
 *      newColumnCardOrder: // value for 'newColumnCardOrder'
 *      oldColumnCardOrder: // value for 'oldColumnCardOrder'
 *   },
 * });
 */
export function useMoveCardMutation(baseOptions?: Apollo.MutationHookOptions<MoveCardMutation, MoveCardMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<MoveCardMutation, MoveCardMutationVariables>(MoveCardDocument, options);
      }
export type MoveCardMutationHookResult = ReturnType<typeof useMoveCardMutation>;
export type MoveCardMutationResult = Apollo.MutationResult<MoveCardMutation>;
export type MoveCardMutationOptions = Apollo.BaseMutationOptions<MoveCardMutation, MoveCardMutationVariables>;
export const ReorderCardsInColumnDocument = gql`
    mutation ReorderCardsInColumn($columnId: ID!, $cardPriorityOrder: [ID!]!) {
  updateColumns(
    where: {externalId: {eq: $columnId}}
    update: {cardPriorityOrder: {set: $cardPriorityOrder}}
  ) {
    info {
      nodesCreated
      nodesDeleted
    }
    columns {
      externalId
      cardPriorityOrder
    }
  }
}
    `;
export type ReorderCardsInColumnMutationFn = Apollo.MutationFunction<ReorderCardsInColumnMutation, ReorderCardsInColumnMutationVariables>;

/**
 * __useReorderCardsInColumnMutation__
 *
 * To run a mutation, you first call `useReorderCardsInColumnMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useReorderCardsInColumnMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [reorderCardsInColumnMutation, { data, loading, error }] = useReorderCardsInColumnMutation({
 *   variables: {
 *      columnId: // value for 'columnId'
 *      cardPriorityOrder: // value for 'cardPriorityOrder'
 *   },
 * });
 */
export function useReorderCardsInColumnMutation(baseOptions?: Apollo.MutationHookOptions<ReorderCardsInColumnMutation, ReorderCardsInColumnMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ReorderCardsInColumnMutation, ReorderCardsInColumnMutationVariables>(ReorderCardsInColumnDocument, options);
      }
export type ReorderCardsInColumnMutationHookResult = ReturnType<typeof useReorderCardsInColumnMutation>;
export type ReorderCardsInColumnMutationResult = Apollo.MutationResult<ReorderCardsInColumnMutation>;
export type ReorderCardsInColumnMutationOptions = Apollo.BaseMutationOptions<ReorderCardsInColumnMutation, ReorderCardsInColumnMutationVariables>;
export const GetCardWithDetailsDocument = gql`
    query GetCardWithDetails($cardId: ID!) {
  columns(where: {cards: {some: {externalId: {eq: $cardId}}}}) {
    externalId
    name
    type
    cards(where: {externalId: {eq: $cardId}}) {
      externalId
      title
      description
      tags
      boardTags
      estimatedHours
      workedHours
      color
      dueDate
      priority
      createdBy
      createdOn
      deletedBy
      deletedOn
      assignee {
        externalId
        name
        email
      }
      commentList {
        externalId
        comments {
          externalId
          content
          createdBy
          createdOn
          modifiedBy
          modifiedOn
          deletedBy
          deletedOn
          attachmentList {
            externalId
            attachments {
              externalId
              name
              url
              uploadedBy
              uploadedOn
            }
          }
          commentList {
            externalId
            comments {
              externalId
              content
              createdBy
              createdOn
              modifiedBy
              modifiedOn
              deletedBy
              deletedOn
            }
          }
        }
      }
      attachmentList {
        externalId
        attachments {
          externalId
          name
          url
          uploadedBy
          uploadedOn
        }
      }
      historyList {
        externalId
        histories {
          externalId
          referenceId
          operation
          dateTime
          before
          after
        }
      }
    }
  }
}
    `;

/**
 * __useGetCardWithDetailsQuery__
 *
 * To run a query within a React component, call `useGetCardWithDetailsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCardWithDetailsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCardWithDetailsQuery({
 *   variables: {
 *      cardId: // value for 'cardId'
 *   },
 * });
 */
export function useGetCardWithDetailsQuery(baseOptions: Apollo.QueryHookOptions<GetCardWithDetailsQuery, GetCardWithDetailsQueryVariables> & ({ variables: GetCardWithDetailsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetCardWithDetailsQuery, GetCardWithDetailsQueryVariables>(GetCardWithDetailsDocument, options);
      }
export function useGetCardWithDetailsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetCardWithDetailsQuery, GetCardWithDetailsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetCardWithDetailsQuery, GetCardWithDetailsQueryVariables>(GetCardWithDetailsDocument, options);
        }
export function useGetCardWithDetailsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetCardWithDetailsQuery, GetCardWithDetailsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetCardWithDetailsQuery, GetCardWithDetailsQueryVariables>(GetCardWithDetailsDocument, options);
        }
export type GetCardWithDetailsQueryHookResult = ReturnType<typeof useGetCardWithDetailsQuery>;
export type GetCardWithDetailsLazyQueryHookResult = ReturnType<typeof useGetCardWithDetailsLazyQuery>;
export type GetCardWithDetailsSuspenseQueryHookResult = ReturnType<typeof useGetCardWithDetailsSuspenseQuery>;
export type GetCardWithDetailsQueryResult = Apollo.QueryResult<GetCardWithDetailsQuery, GetCardWithDetailsQueryVariables>;
export const AddCommentDocument = gql`
    mutation AddComment($commentInput: CommentCreateInput!) {
  createComments(input: [$commentInput]) {
    info {
      nodesCreated
    }
    comments {
      externalId
      content
      createdBy
      createdOn
      modifiedBy
      modifiedOn
      deletedBy
      deletedOn
      attachmentList {
        externalId
        attachments {
          externalId
          name
          url
          uploadedBy
          uploadedOn
        }
      }
      commentList {
        externalId
        comments {
          externalId
          content
          createdBy
          createdOn
          modifiedBy
          modifiedOn
          deletedBy
          deletedOn
        }
      }
    }
  }
}
    `;
export type AddCommentMutationFn = Apollo.MutationFunction<AddCommentMutation, AddCommentMutationVariables>;

/**
 * __useAddCommentMutation__
 *
 * To run a mutation, you first call `useAddCommentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddCommentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addCommentMutation, { data, loading, error }] = useAddCommentMutation({
 *   variables: {
 *      commentInput: // value for 'commentInput'
 *   },
 * });
 */
export function useAddCommentMutation(baseOptions?: Apollo.MutationHookOptions<AddCommentMutation, AddCommentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddCommentMutation, AddCommentMutationVariables>(AddCommentDocument, options);
      }
export type AddCommentMutationHookResult = ReturnType<typeof useAddCommentMutation>;
export type AddCommentMutationResult = Apollo.MutationResult<AddCommentMutation>;
export type AddCommentMutationOptions = Apollo.BaseMutationOptions<AddCommentMutation, AddCommentMutationVariables>;
export const AddAttachmentDocument = gql`
    mutation AddAttachment($attachmentInput: AttachmentCreateInput!) {
  createAttachments(input: [$attachmentInput]) {
    info {
      nodesCreated
    }
    attachments {
      externalId
      name
      url
      uploadedBy
      uploadedOn
    }
  }
}
    `;
export type AddAttachmentMutationFn = Apollo.MutationFunction<AddAttachmentMutation, AddAttachmentMutationVariables>;

/**
 * __useAddAttachmentMutation__
 *
 * To run a mutation, you first call `useAddAttachmentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddAttachmentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addAttachmentMutation, { data, loading, error }] = useAddAttachmentMutation({
 *   variables: {
 *      attachmentInput: // value for 'attachmentInput'
 *   },
 * });
 */
export function useAddAttachmentMutation(baseOptions?: Apollo.MutationHookOptions<AddAttachmentMutation, AddAttachmentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddAttachmentMutation, AddAttachmentMutationVariables>(AddAttachmentDocument, options);
      }
export type AddAttachmentMutationHookResult = ReturnType<typeof useAddAttachmentMutation>;
export type AddAttachmentMutationResult = Apollo.MutationResult<AddAttachmentMutation>;
export type AddAttachmentMutationOptions = Apollo.BaseMutationOptions<AddAttachmentMutation, AddAttachmentMutationVariables>;
export const GetMeAndBoardsDocument = gql`
    query GetMeAndBoards {
  me {
    externalId
    email
    name
  }
  boards {
    externalId
    name
    createdOn
  }
}
    `;

/**
 * __useGetMeAndBoardsQuery__
 *
 * To run a query within a React component, call `useGetMeAndBoardsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMeAndBoardsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMeAndBoardsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetMeAndBoardsQuery(baseOptions?: Apollo.QueryHookOptions<GetMeAndBoardsQuery, GetMeAndBoardsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetMeAndBoardsQuery, GetMeAndBoardsQueryVariables>(GetMeAndBoardsDocument, options);
      }
export function useGetMeAndBoardsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetMeAndBoardsQuery, GetMeAndBoardsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetMeAndBoardsQuery, GetMeAndBoardsQueryVariables>(GetMeAndBoardsDocument, options);
        }
export function useGetMeAndBoardsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetMeAndBoardsQuery, GetMeAndBoardsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetMeAndBoardsQuery, GetMeAndBoardsQueryVariables>(GetMeAndBoardsDocument, options);
        }
export type GetMeAndBoardsQueryHookResult = ReturnType<typeof useGetMeAndBoardsQuery>;
export type GetMeAndBoardsLazyQueryHookResult = ReturnType<typeof useGetMeAndBoardsLazyQuery>;
export type GetMeAndBoardsSuspenseQueryHookResult = ReturnType<typeof useGetMeAndBoardsSuspenseQuery>;
export type GetMeAndBoardsQueryResult = Apollo.QueryResult<GetMeAndBoardsQuery, GetMeAndBoardsQueryVariables>;
export const CreateBoardDocument = gql`
    mutation CreateBoard($input: [BoardCreateInput!]!) {
  createBoards(input: $input) {
    info {
      nodesCreated
    }
    boards {
      externalId
      name
      createdOn
    }
  }
}
    `;
export type CreateBoardMutationFn = Apollo.MutationFunction<CreateBoardMutation, CreateBoardMutationVariables>;

/**
 * __useCreateBoardMutation__
 *
 * To run a mutation, you first call `useCreateBoardMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateBoardMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createBoardMutation, { data, loading, error }] = useCreateBoardMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateBoardMutation(baseOptions?: Apollo.MutationHookOptions<CreateBoardMutation, CreateBoardMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateBoardMutation, CreateBoardMutationVariables>(CreateBoardDocument, options);
      }
export type CreateBoardMutationHookResult = ReturnType<typeof useCreateBoardMutation>;
export type CreateBoardMutationResult = Apollo.MutationResult<CreateBoardMutation>;
export type CreateBoardMutationOptions = Apollo.BaseMutationOptions<CreateBoardMutation, CreateBoardMutationVariables>;
export const CreateBoardLinkedToCardDocument = gql`
    mutation CreateBoardLinkedToCard($boardInput: BoardCreateInput!, $cardId: ID!, $boardId: ID!) {
  createBoards(input: [$boardInput]) {
    info {
      nodesCreated
    }
    boards {
      externalId
      name
      createdOn
    }
  }
  updateCards(
    where: {externalId: {eq: $cardId}}
    update: {linkedBoards: {connect: {where: {node: {externalId: {eq: $boardId}}}}}}
  ) {
    cards {
      externalId
      linkedBoards {
        externalId
        name
      }
    }
  }
}
    `;
export type CreateBoardLinkedToCardMutationFn = Apollo.MutationFunction<CreateBoardLinkedToCardMutation, CreateBoardLinkedToCardMutationVariables>;

/**
 * __useCreateBoardLinkedToCardMutation__
 *
 * To run a mutation, you first call `useCreateBoardLinkedToCardMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateBoardLinkedToCardMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createBoardLinkedToCardMutation, { data, loading, error }] = useCreateBoardLinkedToCardMutation({
 *   variables: {
 *      boardInput: // value for 'boardInput'
 *      cardId: // value for 'cardId'
 *      boardId: // value for 'boardId'
 *   },
 * });
 */
export function useCreateBoardLinkedToCardMutation(baseOptions?: Apollo.MutationHookOptions<CreateBoardLinkedToCardMutation, CreateBoardLinkedToCardMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateBoardLinkedToCardMutation, CreateBoardLinkedToCardMutationVariables>(CreateBoardLinkedToCardDocument, options);
      }
export type CreateBoardLinkedToCardMutationHookResult = ReturnType<typeof useCreateBoardLinkedToCardMutation>;
export type CreateBoardLinkedToCardMutationResult = Apollo.MutationResult<CreateBoardLinkedToCardMutation>;
export type CreateBoardLinkedToCardMutationOptions = Apollo.BaseMutationOptions<CreateBoardLinkedToCardMutation, CreateBoardLinkedToCardMutationVariables>;
export const CreateColumnsDocument = gql`
    mutation CreateColumns($input: [ColumnCreateInput!]!) {
  createColumns(input: $input) {
    info {
      nodesCreated
    }
    columns {
      externalId
      name
      type
    }
  }
}
    `;
export type CreateColumnsMutationFn = Apollo.MutationFunction<CreateColumnsMutation, CreateColumnsMutationVariables>;

/**
 * __useCreateColumnsMutation__
 *
 * To run a mutation, you first call `useCreateColumnsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateColumnsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createColumnsMutation, { data, loading, error }] = useCreateColumnsMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateColumnsMutation(baseOptions?: Apollo.MutationHookOptions<CreateColumnsMutation, CreateColumnsMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateColumnsMutation, CreateColumnsMutationVariables>(CreateColumnsDocument, options);
      }
export type CreateColumnsMutationHookResult = ReturnType<typeof useCreateColumnsMutation>;
export type CreateColumnsMutationResult = Apollo.MutationResult<CreateColumnsMutation>;
export type CreateColumnsMutationOptions = Apollo.BaseMutationOptions<CreateColumnsMutation, CreateColumnsMutationVariables>;