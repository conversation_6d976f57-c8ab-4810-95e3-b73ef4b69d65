import neo4j, { Driver, Session } from 'neo4j-driver';

export class DatabaseConnection {
  private driver: Driver;

  constructor(uri: string, username: string, password: string) {
    this.driver = neo4j.driver(
      uri,
      neo4j.auth.basic(username, password)
    );
  }

  getDriver(): Driver {
    return this.driver;
  }

  getSession(): Session {
    return this.driver.session();
  }

  async close(): Promise<void> {
    await this.driver.close();
  }

  async verifyConnectivity(): Promise<void> {
    const session = this.getSession();
    try {
      await session.run('RETURN 1');
    } finally {
      await session.close();
    }
  }
}

// Singleton instance
let dbConnection: DatabaseConnection | null = null;

export function initializeDatabase(uri: string, username: string, password: string): DatabaseConnection {
  if (!dbConnection) {
    dbConnection = new DatabaseConnection(uri, username, password);
  }
  return dbConnection;
}

export function getDatabaseConnection(): DatabaseConnection {
  if (!dbConnection) {
    throw new Error('Database not initialized. Call initializeDatabase first.');
  }
  return dbConnection;
}

export async function closeDatabaseConnection(): Promise<void> {
  if (dbConnection) {
    await dbConnection.close();
    dbConnection = null;
  }
}