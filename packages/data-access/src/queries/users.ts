import { Driver } from 'neo4j-driver';
import { AuthProvider, User } from '@kanban/graphql-schema';
import * as bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

export class UserQueries {
  constructor(private driver: Driver) {}

  async findUserByEmail(email: string): Promise<any | null> {
    const session = this.driver.session();
    try {
      const result = await session.run(
        "MATCH (u:User {email: $email}) RETURN u",
        { email }
      );

      if (result.records.length === 0) {
        return null;
      }

      return result.records[0].get("u").properties;
    } finally {
      await session.close();
    }
  }

  async findUserByExternalId(externalId: string): Promise<any | null> {
    const session = this.driver.session();
    try {
      const result = await session.run(
        "MATCH (u:User {externalId: $externalId}) RETURN u",
        { externalId }
      );

      if (result.records.length === 0) {
        return null;
      }

      return result.records[0].get("u").properties;
    } finally {
      await session.close();
    }
  }

  async findUserAuth(userId: string, provider: AuthProvider): Promise<any | null> {
    const session = this.driver.session();
    try {
      const result = await session.run(
        `MATCH (u:User {externalId: $userId})-[:AUTHENTICATED_BY]->(ua:UserAuth {provider: $provider})
         RETURN ua`,
        { userId, provider }
      );

      if (result.records.length === 0) {
        return null;
      }

      return result.records[0].get("ua").properties;
    } finally {
      await session.close();
    }
  }

  async findUserByEmailAndProvider(email: string, provider: AuthProvider): Promise<{user: any, userAuth: any} | null> {
    const session = this.driver.session();
    try {
      const result = await session.run(
        `MATCH (u:User)-[:AUTHENTICATED_BY]->(ua:UserAuth {email: $email, provider: $provider})
         RETURN u, ua`,
        { email, provider }
      );

      if (result.records.length === 0) {
        return null;
      }

      const user = result.records[0].get("u").properties;
      const userAuth = result.records[0].get("ua").properties;

      return { user, userAuth };
    } finally {
      await session.close();
    }
  }

  async createUserWithLocalAuth(email: string, name: string, password: string): Promise<string> {
    const session = this.driver.session();
    const userId = uuidv4();
    const authId = uuidv4();
    const now = new Date().toISOString();
    const hashedPassword = await bcrypt.hash(password, 12);

    try {
      await session.run(
        `CREATE (u:User {
           externalId: $userId,
           email: $email,
           name: $name,
           createdOn: $now,
           createdBy: $userId
         })
         CREATE (ua:UserAuth {
           externalId: $authId,
           provider: $provider,
           providerUserId: $email,
           email: $email,
           password: $password,
           createdOn: $now,
           createdBy: $userId
         })
         CREATE (u)-[:AUTHENTICATED_BY]->(ua)`,
        {
          userId,
          authId,
          email,
          name,
          provider: AuthProvider.Local,
          password: hashedPassword,
          now,
        }
      );

      return userId;
    } finally {
      await session.close();
    }
  }

  async createUserWithGoogleAuth(email: string, name: string): Promise<string> {
    const session = this.driver.session();
    const userId = uuidv4();
    const authId = uuidv4();
    const now = new Date().toISOString();

    try {
      await session.run(
        `CREATE (u:User {
           externalId: $userId,
           email: $email,
           name: $name,
           createdOn: $now,
           createdBy: $userId
         })
         CREATE (ua:UserAuth {
           externalId: $authId,
           provider: $provider,
           providerUserId: $email,
           email: $email,
           createdOn: $now,
           createdBy: $userId
         })
         CREATE (u)-[:AUTHENTICATED_BY]->(ua)`,
        {
          userId,
          authId,
          email,
          name,
          provider: AuthProvider.Google,
          now,
        }
      );

      return userId;
    } finally {
      await session.close();
    }
  }

  async addLocalAuthToUser(userId: string, email: string, password: string): Promise<void> {
    const session = this.driver.session();
    const authId = uuidv4();
    const now = new Date().toISOString();
    const hashedPassword = await bcrypt.hash(password, 12);

    try {
      await session.run(
        `MATCH (u:User {externalId: $userId})
         CREATE (ua:UserAuth {
           externalId: $authId,
           provider: $provider,
           providerUserId: $email,
           email: $email,
           password: $password,
           createdOn: $now,
           createdBy: $userId
         })
         CREATE (u)-[:AUTHENTICATED_BY]->(ua)`,
        {
          userId,
          authId,
          provider: AuthProvider.Local,
          email,
          password: hashedPassword,
          now,
        }
      );
    } finally {
      await session.close();
    }
  }

  async addGoogleAuthToUser(userId: string, email: string): Promise<void> {
    const session = this.driver.session();
    const authId = uuidv4();
    const now = new Date().toISOString();

    try {
      await session.run(
        `MATCH (u:User {externalId: $userId})
         CREATE (ua:UserAuth {
           externalId: $authId,
           provider: $provider,
           providerUserId: $email,
           email: $email,
           createdOn: $now,
           createdBy: $userId
         })
         CREATE (u)-[:AUTHENTICATED_BY]->(ua)`,
        {
          userId,
          authId,
          provider: AuthProvider.Google,
          email,
          now,
        }
      );
    } finally {
      await session.close();
    }
  }

  async updateGoogleUserAuth(userId: string, email: string): Promise<void> {
    const session = this.driver.session();

    try {
      await session.run(
        `MATCH (u:User {externalId: $userId})-[:AUTHENTICATED_BY]->(ua:UserAuth {provider: $provider})
         SET ua.email = $email`,
        {
          userId,
          provider: AuthProvider.Google,
          email,
        }
      );
    } finally {
      await session.close();
    }
  }

  async updateUserProfile(userId: string, name: string): Promise<void> {
    const session = this.driver.session();

    try {
      await session.run(
        `MATCH (u:User {externalId: $userId})
         SET u.name = $name`,
        {
          userId,
          name,
        }
      );
    } finally {
      await session.close();
    }
  }

  async validatePassword(hashedPassword: string, plainPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }
}