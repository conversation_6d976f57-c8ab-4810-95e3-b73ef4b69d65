import { Driver } from "neo4j-driver";

export class BoardQueries {
  constructor(private driver: Driver) {}

  async linkCardToBoard(
    cardId: string,
    boardId: string
  ): Promise<{ success: boolean; message: string }> {
    const session = this.driver.session();

    try {
      const result = await session.run(
        `
        MATCH (card:Card {externalId: $cardId})
        MATCH (board:Board {externalId: $boardId})
        MERGE (board)-[:IS_WORK_FOR]->(card)
        RETURN card, board
        `,
        { cardId, boardId }
      );

      if (result.records.length === 0) {
        throw new Error("Card or Board not found");
      }

      return {
        success: true,
        message: "Card linked to board successfully",
      };
    } finally {
      await session.close();
    }
  }
}
