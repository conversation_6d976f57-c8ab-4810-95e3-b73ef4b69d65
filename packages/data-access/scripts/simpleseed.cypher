// Seed script for Kanban application
// Creates a test user and sample board for development/testing

// Create Test User with authentication
MERGE (u:User { externalId: "11111111-1111-1111-1111-111111111111" })
  ON CREATE SET
u.email = "<EMAIL>",
u.name = "Test User",
u.createdOn = datetime(),
u.createdBy = "11111111-1111-1111-1111-111111111111"

// Create UserAuth for the test user with LOCAL provider
MERGE (ua:UserAuth {
  externalId: "11111111-1111-1111-1111-111111111112",
  provider: "LOCAL",
  email: "<EMAIL>"
  })
    ON CREATE SET
  ua.providerUserId = "<EMAIL>",
  ua.password = "$2b$12$GTwMSKe1NsWwJWmVDkZuEOi2lqVqZYV3qhyTLoZaoYOb9uVGmy9Ru",
  ua.createdOn = datetime(),
  ua.createdBy = "11111111-1111-1111-1111-111111111111"
  
// Link User to User<PERSON><PERSON>
  MERGE (u)-[:AUTHENTICATED_BY]->(ua)
  
// Create Sample Board
  MERGE (b:Board { externalId: "11111111-1111-1111-1111-111111111111" })
    ON CREATE SET
  b.name = "Sample Kanban Board",
  b.columnOrder = ["11111111-1111-1111-1111-111111111111", "11111111-1111-1111-1111-111111111112", "11111111-1111-1111-1111-111111111113"],
  b.createdOn = datetime(),
  b.createdBy = "11111111-1111-1111-1111-111111111111"
  
// Create relationship between User and Board (assuming user owns the board)
  MERGE (u)-[:OWNS]->(b)
  
// Create Sample Columns for the Board
  MERGE (c1:Column { externalId: "11111111-1111-1111-1111-111111111111", name: "To Do" })
    ON CREATE SET
  c1.createdOn = datetime(),
  c1.createdBy = "11111111-1111-1111-1111-111111111111"
  c1.cardPriorityOrder = [],
    ON MATCH SET
  c1.type = 'NOT_STARTED'
  
  MERGE (c2:Column { externalId: "11111111-1111-1111-1111-111111111112", name: "Doing" })
    ON CREATE SET
  c2.createdOn = datetime(),
  c2.createdBy = "11111111-1111-1111-1111-111111111111"
  c2.cardPriorityOrder = [],
    ON MATCH SET
  c2.type = 'IN_PROGRESS'
  
  MERGE (c3:Column { externalId: "11111111-1111-1111-1111-111111111113", name: "Done" })
    ON CREATE SET
  c3.createdOn = datetime(),
  c3.createdBy = "11111111-1111-1111-1111-111111111111"
  c3.cardPriorityOrder = [],
    ON MATCH SET
  c3.type = 'COMPLETED'
  
// Link Columns to Board
  MERGE (b)<-[:IS_STATE_IN]-(c1)
  MERGE (b)<-[:IS_STATE_IN]-(c2)
  MERGE (b)<-[:IS_STATE_IN]-(c3)
  
  RETURN u, ua, b, c1, c2, c3;
