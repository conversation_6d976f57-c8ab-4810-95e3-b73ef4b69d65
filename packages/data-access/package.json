{"name": "@kanban/data-access", "version": "1.0.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "../../node_modules/.bin/tsc", "dev": "../../node_modules/.bin/tsc --watch", "clean": "rm -rf dist", "type-check": "../../node_modules/.bin/tsc --noEmit"}, "dependencies": {"neo4j-driver": "^5.28.1", "@kanban/graphql-schema": "workspace:*", "bcryptjs": "^2.4.3"}, "devDependencies": {"@kanban/tsconfig": "workspace:*", "@types/bcryptjs": "^2.4.6"}}