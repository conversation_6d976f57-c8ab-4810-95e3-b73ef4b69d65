// Theme
export {
  theme,
  lightTheme,
  darkTheme,
  createThemeWithMode,
  default as defaultTheme,
} from "./theme";

// Theme types
export type { PaletteMode } from "@mui/material/styles";

// Components
export { ThemeProvider } from "./components/ThemeProvider";
export { Button } from "./components/Button";
export { Card } from "./components/Card";
export { TextField } from "./components/TextField";
export { Container } from "./components/Container";
export { HoverButton } from "./components/HoverButton";

// Re-export useful Material-UI components and types
export {
  Box,
  Stack,
  Grid,
  Typography,
  Divider,
  Alert,
  CircularProgress,
  LinearProgress,
  Backdrop,
  AppBar,
  Toolbar,
  Menu,
  MenuItem,
  Avatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Paper,
  List,
  ListItem,
  ListItemText,
  Chip,
} from "@mui/material";

// Re-export commonly used icons
export {
  Add,
  Delete,
  Edit,
  Save,
  Cancel,
  Brightness4,
  Brightness7,
  Home,
  Person,
  Logout,
  Login,
  MoreVert,
  AccountTree,
  KeyboardArrowDown,
  KeyboardArrowUp,
  KeyboardArrowLeft,
  KeyboardArrowRight,
} from "@mui/icons-material";

// Types
export type { ButtonProps } from "./components/Button";
export type { CardProps } from "./components/Card";
export type { TextFieldProps } from "./components/TextField";
export type { ContainerProps } from "./components/Container";
