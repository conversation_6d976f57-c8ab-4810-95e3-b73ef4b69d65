import { createTheme, ThemeOptions, PaletteMode } from '@mui/material/styles';

// Kanban brand colors (shared between themes)
const brandColors = {
  primary: {
    main: '#0077b6',
    light: '#3b82f6',
    dark: '#1d4ed8',
  },
  secondary: {
    main: '#10b981',
    light: '#34d399',
    dark: '#059669',
  },
  error: {
    main: '#dc3545',
    light: '#ef4444',
    dark: '#b91c1c',
  },
  warning: {
    main: '#f59e0b',
    light: '#fbbf24',
    dark: '#d97706',
  },
  success: {
    main: '#10b981',
    light: '#34d399',
    dark: '#059669',
  },
};

// Light theme palette
const lightPalette = {
  mode: 'light' as PaletteMode,
  ...brandColors,
  background: {
    default: '#f5f5f5',
    paper: '#ffffff',
    primary: '#e5e5e5',
    secondary: '#e0e5e5'
  },
  text: {
    primary: '#333333',
    secondary: '#666666',
  },
  divider: '#e0e0e0',
};

// Dark theme palette
const darkPalette = {
  mode: 'dark' as PaletteMode,
  ...brandColors,
  background: {
    default: '#121212',
    paper: '#2d2d2d',
    primary: '#2a2a2a',
    secondary: '#333333'
  },
  text: {
    primary: '#ffffff',
    secondary: '#b3b3b3',
  },
  divider: '#404040',
};

const typography = {
  fontFamily: 'Arial, sans-serif',
  h1: {
    fontSize: '2rem',
    fontWeight: 600,
    lineHeight: 1.2,
  },
  h2: {
    fontSize: '1.5rem',
    fontWeight: 600,
    lineHeight: 1.3,
  },
  h3: {
    fontSize: '1.25rem',
    fontWeight: 600,
    lineHeight: 1.4,
  },
  body1: {
    fontSize: '1rem',
    lineHeight: 1.5,
  },
  body2: {
    fontSize: '0.9rem',
    lineHeight: 1.4,
  },
  caption: {
    fontSize: '0.8rem',
    lineHeight: 1.3,
  },
};

const shape = {
  borderRadius: 4,
};

const spacing = 8;

const components = {
  MuiButton: {
    styleOverrides: {
      root: {
        textTransform: 'none' as const,
        borderRadius: 4,
        padding: '0.5rem 1rem',
      },
    },
  },
  MuiCard: {
    styleOverrides: {
      root: {
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        '&:hover': {
          boxShadow: '0 4px 8px rgba(0,0,0,0.15)',
        },
      },
    },
  },
  MuiTextField: {
    styleOverrides: {
      root: {
        '& .MuiOutlinedInput-root': {
          borderRadius: 4,
        },
      },
    },
  },
};

// Create theme function that accepts mode
const createKanbanTheme = (mode: PaletteMode = 'light'): ThemeOptions => ({
  palette: mode === 'light' ? lightPalette : darkPalette,
  typography,
  shape,
  spacing,
  components,
});

// Export both light and dark themes
export const lightTheme = createTheme(createKanbanTheme('light'));
export const darkTheme = createTheme(createKanbanTheme('dark'));

// Export default theme (light)
export const theme = lightTheme;

// Export theme creator function for dynamic theme switching
export const createThemeWithMode = (mode: PaletteMode) => createTheme(createKanbanTheme(mode));
export default theme;