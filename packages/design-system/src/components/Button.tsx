import React from 'react';
import { Button as MuiButton, ButtonProps as MuiButtonProps } from '@mui/material';

export interface ButtonProps extends Omit<MuiButtonProps, 'variant' | 'color'> {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  loading?: boolean;
}

export const Button: React.FC<ButtonProps> = ({ 
  variant = 'primary', 
  loading = false,
  disabled,
  children,
  ...props 
}) => {
  const getVariantProps = (): { variant: 'contained' | 'outlined'; color: 'primary' | 'secondary' | 'error' } => {
    switch (variant) {
      case 'primary':
        return { variant: 'contained', color: 'primary' };
      case 'secondary':
        return { variant: 'contained', color: 'secondary' };
      case 'danger':
        return { variant: 'contained', color: 'error' };
      case 'ghost':
        return { variant: 'outlined', color: 'primary' };
      default:
        return { variant: 'contained', color: 'primary' };
    }
  };

  return (
    <MuiButton
      {...getVariantProps()}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? 'Loading...' : children}
    </MuiButton>
  );
};

export default Button;