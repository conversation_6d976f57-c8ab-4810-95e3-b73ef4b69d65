import React from 'react';
import { Container as MuiContainer, ContainerProps as MuiContainerProps } from '@mui/material';

export interface ContainerProps extends MuiContainerProps {
  centered?: boolean;
}

export const Container: React.FC<ContainerProps> = ({ 
  centered = false,
  children,
  sx,
  ...props 
}) => {
  const centeredStyles = centered ? {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
  } : {};

  return (
    <MuiContainer
      maxWidth="lg"
      sx={{
        ...centeredStyles,
        ...sx,
      }}
      {...props}
    >
      {children}
    </MuiContainer>
  );
};

export default Container;