import React from 'react';
import { Card as MuiCard, CardContent, CardProps as MuiCardProps } from '@mui/material';

export interface CardProps extends MuiCardProps {
  children: React.ReactNode;
  padding?: 'none' | 'small' | 'medium' | 'large';
}

export const Card: React.FC<CardProps> = ({ 
  children, 
  padding = 'medium',
  ...props 
}) => {
  const getPadding = () => {
    switch (padding) {
      case 'none':
        return 0;
      case 'small':
        return 1;
      case 'medium':
        return 2;
      case 'large':
        return 3;
      default:
        return 2;
    }
  };

  return (
    <MuiCard {...props}>
      {padding === 'none' ? (
        children
      ) : (
        <CardContent sx={{ padding: getPadding() }}>
          {children}
        </CardContent>
      )}
    </MuiCard>
  );
};

export default Card;