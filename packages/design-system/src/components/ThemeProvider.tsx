import React from 'react';
import { ThemeProvider as MuiThemeProvider, CssBaseline, Theme } from '@mui/material';
import { theme } from '../theme';

interface ThemeProviderProps {
  children: React.ReactNode;
  theme?: Theme;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children, theme: customTheme }) => {
  return (
    <MuiThemeProvider theme={customTheme || theme}>
      <CssBaseline />
      {children}
    </MuiThemeProvider>
  );
};

export default ThemeProvider;