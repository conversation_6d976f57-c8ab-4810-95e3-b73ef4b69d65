import React from "react";
import { IconButton, IconButtonProps } from "@mui/material";
import { SxProps, Theme } from "@mui/material/styles";

type HoverButtonPosition =
  | "top-right"
  | "bottom-center"
  | "top-left"
  | "bottom-left"
  | "bottom-right"
  | "top-center";

interface HoverButtonProps extends Omit<IconButtonProps, "sx"> {
  position?: HoverButtonPosition;
  withBackground?: boolean;
  animationName?: string;
  sx?: SxProps<Theme>;
}

const getPositionStyles = (
  position: HoverButtonPosition,
  withBackground: boolean
) => {
  const baseTransform = withBackground ? "scale(0.8)" : "scale(0.8)";
  const hoverTransform = withBackground ? "scale(1)" : "scale(1)";

  switch (position) {
    case "top-right":
      return {
        top: 4,
        right: 4,
        transform: baseTransform,
        "&:hover": {
          transform: hoverTransform,
        },
      };
    case "bottom-center":
      return {
        bottom: 4,
        left: "50%",
        transform: `translateX(-50%) ${baseTransform}`,
        "&:hover": {
          transform: `translateX(-50%) ${hoverTransform}`,
        },
      };
    case "top-left":
      return {
        top: 4,
        left: 4,
        transform: baseTransform,
        "&:hover": {
          transform: hoverTransform,
        },
      };
    case "bottom-left":
      return {
        bottom: 4,
        left: 4,
        transform: baseTransform,
        "&:hover": {
          transform: hoverTransform,
        },
      };
    case "bottom-right":
      return {
        bottom: 4,
        right: 4,
        transform: baseTransform,
        "&:hover": {
          transform: hoverTransform,
        },
      };
    case "top-center":
      return {
        top: 4,
        left: "50%",
        transform: `translateX(-50%) ${baseTransform}`,
        "&:hover": {
          transform: `translateX(-50%) ${hoverTransform}`,
        },
      };
    default:
      return {
        transform: baseTransform,
        "&:hover": {
          transform: hoverTransform,
        },
      };
  }
};

const getAnimationKeyframes = (
  position: HoverButtonPosition,
  withBackground: boolean
) => {
  const baseTransform = withBackground ? "scale(0.8)" : "scale(0.8)";
  const finalTransform = withBackground ? "scale(1)" : "scale(1)";

  if (position === "bottom-center" || position === "top-center") {
    return {
      "0%": {
        opacity: 0,
        transform: `translateX(-50%) ${baseTransform}`,
      },
      "100%": {
        opacity: 1,
        transform: `translateX(-50%) ${finalTransform}`,
      },
    };
  }

  return {
    "0%": {
      opacity: 0,
      transform: baseTransform,
    },
    "100%": {
      opacity: 1,
      transform: finalTransform,
    },
  };
};

export const HoverButton: React.FC<HoverButtonProps> = ({
  position = "top-right",
  withBackground = false,
  animationName,
  sx,
  children,
  ...props
}) => {
  const positionStyles = getPositionStyles(position, withBackground);
  const keyframeName =
    animationName || `fadeInScale${position.replace("-", "")}`;
  const animationKeyframes = getAnimationKeyframes(position, withBackground);

  const combinedSx: SxProps<Theme> = {
    position: "absolute",
    ...positionStyles,
    ...(withBackground && {
      backgroundColor: "rgba(255, 255, 255, 0.9)",
      "&:hover": {
        ...positionStyles["&:hover"],
        backgroundColor: "rgba(255, 255, 255, 1)",
      },
    }),
    opacity: 0,
    transition: "all 0.3s ease-in-out",
    animation: `${keyframeName} 0.3s ease-in-out forwards`,
    zIndex: 1,
    [`@keyframes ${keyframeName}`]: animationKeyframes,
    ...sx,
  };

  return (
    <IconButton size="small" sx={combinedSx} {...props}>
      {children}
    </IconButton>
  );
};
