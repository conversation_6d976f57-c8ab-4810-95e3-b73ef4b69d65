{"name": "@kanban/design-system", "version": "1.0.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist"}, "dependencies": {"@mui/material": "^6.2.0", "@mui/icons-material": "^6.2.0", "@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5"}, "devDependencies": {"@kanban/tsconfig": "workspace:*"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}}