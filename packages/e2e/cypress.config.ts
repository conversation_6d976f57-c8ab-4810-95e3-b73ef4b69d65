import { defineConfig } from "cypress";
import * as dotenv from "dotenv";
import * as path from "path";

// Load environment variables from root .env.local
dotenv.config({ path: path.resolve(__dirname, "../../.env.local") });

export default defineConfig({
  e2e: {
    baseUrl: "http://localhost:3001",
    supportFile: "cypress/support/e2e.ts",
    specPattern: "cypress/e2e/**/*.cy.{js,jsx,ts,tsx}",
    downloadsFolder: "cypress/downloads",
    screenshotsFolder: "cypress/screenshots",
    videosFolder: "cypress/videos",
    fixturesFolder: "cypress/fixtures",
    viewportWidth: 1280,
    viewportHeight: 720,
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    video: true,
    screenshotOnRunFailure: true,
    // Cypress 14 cross-origin configuration
    chromeWebSecurity: false,
    experimentalOriginDependencies: true,
    env: {
      API_URL: "http://localhost:4000/graphql",
      AUTH_URL: "http://localhost:3003",
      UI_URL: "http://localhost:3001",
      CYPRESS_USER: process.env.CYPRESS_USER || (() => { throw new Error('CYPRESS_USER environment variable is required') })(),
      CYPRESS_PASSWORD: process.env.CYPRESS_PASSWORD || (() => { throw new Error('CYPRESS_PASSWORD environment variable is required') })(),
    },
    setupNodeEvents(on, config) {
      // implement node event listeners here
      on("task", {
        log(message) {
          console.log(message);
          return null;
        },
      });
    },
  },
});
