{"name": "@kanban/e2e", "version": "1.0.0", "private": true, "scripts": {"cy:open": "cypress open", "cy:run": "cypress run", "cy:run:api": "cypress run --spec 'cypress/e2e/api/**/*'", "cy:run:ui": "cypress run --spec 'cypress/e2e/ui/**/*'", "test": "cypress run", "test:e2e": "cypress run", "test:headed": "cypress run --headed", "clean": "rm -rf cypress/screenshots cypress/videos cypress/downloads/*"}, "dependencies": {"@kanban/graphql-schema": "workspace:*"}, "devDependencies": {"@kanban/tsconfig": "workspace:*", "@types/node": "^20.19.9", "cypress": "^14.5.3", "dotenv": "^17.2.1", "typescript": "^5.8.3"}}