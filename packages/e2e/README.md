# Kanban E2E Tests

This package contains end-to-end tests for the Kanban application using Cypress.

## Overview

The e2e tests cover both API and UI functionality:

- **API Tests**: Direct GraphQL endpoint testing for authentication, queries, and mutations
- **UI Tests**: Full browser-based testing of user flows across the Auth and UI applications

## Test Structure

```
cypress/
├── e2e/
│   ├── api/                 # API endpoint tests
│   │   ├── health.cy.ts     # API health check tests
│   │   ├── authentication.cy.ts  # Auth mutations tests
│   │   └── queries.cy.ts    # GraphQL queries tests
│   └── ui/                  # UI flow tests
│       ├── authentication.cy.ts  # UI auth flow tests
│       └── navigation.cy.ts      # Cross-app navigation tests
├── fixtures/                # Test data
├── support/                 # Custom commands and utilities
└── downloads/              # Downloaded files during tests
```

## Running Tests

### Prerequisites

Make sure all applications are running:

```bash
# Start all services with Docker Compose
docker-compose --env-file .env.local up

# Or start individual services
pnpm dev:api    # API on localhost:4000
pnpm dev:auth   # Auth on localhost:3003  
pnpm dev:ui     # UI on localhost:3001
```

### Test Commands

```bash
# Run all e2e tests
pnpm test:e2e

# Run API tests only
pnpm test:e2e:api

# Run UI tests only  
pnpm test:e2e:ui

# Open Cypress GUI
pnpm test:e2e:open

# Run from root directory
npm run test:e2e
npm run test:e2e:open
```

## Custom Commands

The test suite includes custom Cypress commands for common operations:

- `cy.graphqlRequest(query, variables, headers)` - Make GraphQL API requests
- `cy.signUpUser(email, password, name)` - Create user via API
- `cy.signInUser(email, password)` - Sign in user via API  
- `cy.setAuthToken(token)` - Set JWT token in localStorage
- `cy.clearAuthToken()` - Clear JWT token from localStorage

## Test Data

Test fixtures are located in `cypress/fixtures/` and include:

- `users.json` - Sample user data for testing

## Environment Configuration

Tests use environment variables defined in `cypress.config.ts`:

- `API_URL`: GraphQL endpoint (default: http://localhost:4000/graphql)
- `AUTH_URL`: Auth application (default: http://localhost:3003)
- `UI_URL`: UI application (default: http://localhost:3001)

## CI/CD Integration

The tests are integrated with Turborepo for:

- Parallel execution with other build tasks
- Caching of test results and artifacts  
- Dependency management (tests run after builds complete)

Screenshots and videos are automatically captured for failed tests and stored in:
- `cypress/screenshots/` 
- `cypress/videos/`