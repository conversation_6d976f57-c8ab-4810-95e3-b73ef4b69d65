// Import commands.js using ES2015 syntax:
import './commands';

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Add global types
declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to make GraphQL requests
       * @example cy.graphqlRequest('query', { variables })
       */
      graphqlRequest(query: string, variables?: Record<string, any>, headers?: Record<string, string>): Chainable<any>;
      
      /**
       * Custom command to sign up a user via API
       * @example cy.signUpUser('<EMAIL>', 'password', 'Test User')
       */
      signUpUser(email: string, password: string, name: string): Chainable<any>;
      
      /**
       * Custom command to sign in a user via API
       * @example cy.signInUser('<EMAIL>', 'password')
       */
      signInUser(email: string, password: string): Chainable<any>;
      
      /**
       * Custom command to set auth token in localStorage
       * @example cy.setAuthToken('jwt-token')
       */
      setAuthToken(token: string): Chainable<void>;
      
      /**
       * Custom command to clear auth token from localStorage
       * @example cy.clearAuthToken()
       */
      clearAuthToken(): Chainable<void>;
      
      /**
       * Custom command to login as the seeded test user
       * @example cy.loginAsSeededUser()
       */
      loginAsSeededUser(): Chainable<any>;
      
      /**
       * Custom command to delete a user by email
       * @example cy.deleteUser('<EMAIL>')
       */
      deleteUser(email: string): Chainable<any>;
      
      /**
       * Custom command to cleanup a test user (won't delete seeded user)
       * @example cy.cleanupTestUser('<EMAIL>')
       */
      cleanupTestUser(email: string): Chainable<void>;
    }
  }
}