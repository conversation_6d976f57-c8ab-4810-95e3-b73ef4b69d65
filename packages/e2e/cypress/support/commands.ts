// Custom commands for Kanban e2e tests

Cypress.Commands.add(
  "graphqlRequest",
  (
    query: string,
    variables: Record<string, any> = {},
    headers: Record<string, string> = {}
  ) => {
    return cy.request({
      method: "POST",
      url: Cypress.env("API_URL"),
      body: {
        query,
        variables,
      },
      headers: {
        "Content-Type": "application/json",
        ...headers,
      },
      failOnStatusCode: false,
    });
  }
);

Cypress.Commands.add(
  "signUpUser",
  (email: string, password: string, name: string) => {
    const mutation = `
    mutation SignUp($input: SignUpInput!) {
      signUp(input: $input) {
        token
        user {
          externalId
          email
          name
        }
      }
    }
  `;

    return cy
      .graphqlRequest(mutation, {
        input: { email, password, name },
      })
      .then((response) => {
        expect(response.status).to.eq(200);
        if (response.body.errors) {
          throw new Error(`GraphQL error: ${response.body.errors[0].message}`);
        }
        expect(response.body.data.signUp).to.exist;
        return response.body.data.signUp;
      });
  }
);

Cypress.Commands.add("signInUser", (email: string, password: string) => {
  const mutation = `
    mutation SignIn($input: SignInInput!) {
      signIn(input: $input) {
        token
        user {
          externalId
          email
          name
        }
      }
    }
  `;

  return cy
    .graphqlRequest(mutation, {
      input: { email, password },
    })
    .then((response) => {
      expect(response.status).to.eq(200);
      if (response.body.errors) {
        throw new Error(`GraphQL error: ${response.body.errors[0].message}`);
      }
      expect(response.body.data.signIn).to.exist;
      return response.body.data.signIn;
    });
});

Cypress.Commands.add("setAuthToken", (token: string) => {
  cy.window().then((win) => {
    win.localStorage.setItem("authToken", token);
  });
});

Cypress.Commands.add("clearAuthToken", () => {
  cy.window().then((win) => {
    win.localStorage.removeItem("authToken");
  });
});

Cypress.Commands.add("loginAsSeededUser", () => {
  const email = Cypress.env("CYPRESS_USER");
  const password = Cypress.env("CYPRESS_PASSWORD");

  return cy.signInUser(email, password);
});

Cypress.Commands.add("deleteUser", (email: string) => {
  const mutation = `
    mutation DeleteUser($email: String!) {
      deleteUsers(where: { email: { eq: $email } }) {
        nodesDeleted
      }
    }
  `;

  return cy.graphqlRequest(mutation, { email }).then((response) => {
    expect(response.status).to.eq(200);
    return response.body.data.deleteUsers;
  });
});

Cypress.Commands.add("cleanupTestUser", (email: string) => {
  if (email && email !== Cypress.env("CYPRESS_USER")) {
    cy.deleteUser(email);
  }
});
