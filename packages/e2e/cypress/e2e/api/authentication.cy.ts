describe("Authentication API", () => {
  let testUser: any;

  beforeEach(() => {
    cy.fixture("users").then((users) => {
      testUser = users.testUser;
    });
  });

  describe("Sign Up", () => {
    it("should successfully sign up a new user", () => {
      const uniqueEmail = `test_${Date.now()}@example.com`;

      cy.signUpUser(uniqueEmail, testUser.password, testUser.name).then(
        (result) => {
          expect(result.token).to.be.a("string");
          expect(result.token).to.have.length.greaterThan(0);
          expect(result.user).to.deep.include({
            email: uniqueEmail,
            name: testUser.name,
          });
          expect(result.user.externalId).to.be.a("string");

          // Cleanup the created user
          cy.cleanupTestUser(uniqueEmail);
        }
      );
    });

    it("should reject duplicate email signup", () => {
      const uniqueEmail = `duplicate_${Date.now()}@example.com`;

      // First signup should succeed
      cy.signUpUser(uniqueEmail, testUser.password, testUser.name);

      // Second signup with same email should fail
      const mutation = `
        mutation SignUp($input: SignUpInput!) {
          signUp(input: $input) {
            token
            user {
              externalId
              email
            }
          }
        }
      `;

      cy.graphqlRequest(mutation, {
        input: {
          email: uniqueEmail,
          password: testUser.password,
          name: testUser.name,
        },
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.errors).to.exist;
        expect(response.body.errors[0].message).to.contain(
          "User with this email and provider already exists"
        );

        // Cleanup the created user
        cy.cleanupTestUser(uniqueEmail);
      });
    });

    it("should validate required fields", () => {
      const mutation = `
        mutation SignUp($input: SignUpInput!) {
          signUp(input: $input) {
            token
            user {
              externalId
            }
          }
        }
      `;

      // Test missing email
      cy.graphqlRequest(mutation, {
        input: { password: testUser.password, name: testUser.name },
      }).then((response) => {
        expect(response.status).to.eq(400);
        expect(response.body.errors).to.exist;
        expect(response.body.errors[0].message).to.contain("email");
      });
    });
  });

  describe("Sign In", () => {
    it("should successfully sign in with valid credentials", () => {
      cy.loginAsSeededUser().then((result) => {
        expect(result.token).to.be.a("string");
        expect(result.token).to.have.length.greaterThan(0);
        expect(result.user).to.deep.include({
          email: Cypress.env("CYPRESS_USER"),
          name: "Test User",
        });
      });
    });

    it("should reject invalid email", () => {
      const mutation = `
        mutation SignIn($input: SignInInput!) {
          signIn(input: $input) {
            token
            user {
              externalId
            }
          }
        }
      `;

      cy.graphqlRequest(mutation, {
        input: {
          email: "<EMAIL>",
          password: testUser.password,
        },
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.errors).to.exist;
        expect(response.body.errors[0].message).to.contain(
          "Invalid email or password"
        );
      });
    });

    it("should reject invalid password", () => {
      const mutation = `
        mutation SignIn($input: SignInInput!) {
          signIn(input: $input) {
            token
            user {
              externalId
            }
          }
        }
      `;

      cy.graphqlRequest(mutation, {
        input: {
          email: Cypress.env("CYPRESS_USER"),
          password: "wrongpassword",
        },
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.errors).to.exist;
        expect(response.body.errors[0].message).to.contain(
          "Invalid email or password"
        );
      });
    });
  });

  describe("Google User Creation", () => {
    let googleUser: any;

    beforeEach(() => {
      cy.fixture("users").then((users) => {
        googleUser = users.googleUser;
      });
    });

    it("should create a new Google user", () => {
      const uniqueEmail = `google_${Date.now()}@example.com`;
      const mutation = `
        mutation CreateOrUpdateGoogleUser($email: String!, $name: String!) {
          createOrUpdateGoogleUser(email: $email, name: $name) {
            token
            user {
              externalId
              email
              name
            }
          }
        }
      `;

      cy.graphqlRequest(mutation, {
        email: uniqueEmail,
        name: googleUser.name,
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data.createOrUpdateGoogleUser).to.exist;

        const result = response.body.data.createOrUpdateGoogleUser;
        expect(result.token).to.be.a("string");
        expect(result.user).to.deep.include({
          email: uniqueEmail,
          name: googleUser.name,
        });
        expect(result.user.externalId).to.be.a("string");

        // Cleanup the created user
        cy.cleanupTestUser(uniqueEmail);
      });
    });

    it("should update existing Google user", () => {
      const uniqueEmail = `google_update_${Date.now()}@example.com`;
      const mutation = `
        mutation CreateOrUpdateGoogleUser($email: String!, $name: String!) {
          createOrUpdateGoogleUser(email: $email, name: $name) {
            token
            user {
              externalId
              email
              name
            }
          }
        }
      `;

      // Create user first
      cy.graphqlRequest(mutation, {
        email: uniqueEmail,
        name: "Original Name",
      });

      // Update user
      cy.graphqlRequest(mutation, {
        email: uniqueEmail,
        name: "Updated Name",
      }).then((response) => {
        expect(response.status).to.eq(200);
        const result = response.body.data.createOrUpdateGoogleUser;
        expect(result.user.name).to.eq("Updated Name");

        // Cleanup the created user
        cy.cleanupTestUser(uniqueEmail);
      });
    });
  });
});
