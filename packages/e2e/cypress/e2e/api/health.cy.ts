describe('API Health Check', () => {
  it('should return healthy status from health endpoint', () => {
    cy.request('GET', 'http://localhost:4000/health')
      .then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body).to.have.property('status', 'healthy');
        expect(response.body).to.have.property('timestamp');
      });
  });

  it('should return API info from root endpoint', () => {
    cy.request('GET', 'http://localhost:4000/')
      .then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body).to.contain('Kanban API is running');
      });
  });

  it('should access GraphQL endpoint', () => {
    const query = `
      query {
        hello
      }
    `;

    cy.graphqlRequest(query)
      .then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data.hello).to.eq('Hello from Kanban API!');
      });
  });
});