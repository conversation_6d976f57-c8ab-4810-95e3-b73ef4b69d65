describe("GraphQL Queries", () => {
  let testUser: any;
  let authToken: string;

  beforeEach(() => {
    cy.fixture("users")
      .then((users) => {
        testUser = users.testUser;

        // Use seeded user for authenticated queries
        return cy.loginAsSeededUser();
      })
      .then((result) => {
        authToken = result.token;
      });
  });

  describe("Hello Query", () => {
    it("should return hello message without authentication", () => {
      const query = `
        query {
          hello
        }
      `;

      cy.graphqlRequest(query).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data.hello).to.eq("Hello from Kanban API!");
      });
    });
  });

  describe("Me Query", () => {
    it("should return current user when authenticated", () => {
      const query = `
        query {
          me {
            externalId
            email
            name
          }
        }
      `;

      cy.graphqlRequest(
        query,
        {},
        {
          Authorization: `Bearer ${authToken}`,
        }
      ).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data.me).to.exist;
        expect(response.body.data.me.email).to.be.a("string");
        expect(response.body.data.me.name).to.eq(testUser.name);
      });
    });

    it("should return null when not authenticated", () => {
      const query = `
        query {
          me {
            externalId
            email
            name
          }
        }
      `;

      cy.graphqlRequest(query).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data.me).to.be.null;
      });
    });

    it("should return null with invalid token", () => {
      const query = `
        query {
          me {
            externalId
            email
            name
          }
        }
      `;

      cy.graphqlRequest(
        query,
        {},
        {
          Authorization: "Bearer invalid-token",
        }
      ).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data.me).to.be.null;
      });
    });
  });

  describe("Boards Query", () => {
    it("should return list of boards", () => {
      const query = `
        query {
          boards {
            externalId
            columnOrder
            columns {
              externalId
              name
            }
          }
        }
      `;

      cy.graphqlRequest(query).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data.boards).to.be.an("array");

        if (response.body.data.boards.length > 0) {
          const board = response.body.data.boards[0];
          expect(board).to.have.property("externalId");
          expect(board).to.have.property("columnOrder");
          expect(board).to.have.property("columns");
          expect(board.columns).to.be.an("array");
        }
      });
    });
  });

  describe("Board Query", () => {
    it("should return specific board by ID using where clause", () => {
      const query = `
        query GetBoard($where: BoardWhere!) {
          boards(where: $where) {
            externalId
            columnOrder
            columns {
              externalId
              name
            }
          }
        }
      `;

      // Using the sample board ID from the in-memory data
      cy.graphqlRequest(query, {
        where: { externalId: { eq: "11111111-1111-1111-1111-111111111111" } },
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data.boards).to.be.an("array");
        expect(response.body.data.boards).to.have.length(1);
        expect(response.body.data.boards[0].externalId).to.eq(
          "11111111-1111-1111-1111-111111111111"
        );
        expect(response.body.data.boards[0].columnOrder).to.be.an("array");
        expect(response.body.data.boards[0].columns).to.be.an("array");
      });
    });

    it("should return empty array for non-existent board", () => {
      const query = `
        query GetBoard($where: BoardWhere!) {
          boards(where: $where) {
            externalId
            columnOrder
          }
        }
      `;

      cy.graphqlRequest(query, {
        where: { externalId: { eq: "non-existent-id" } },
      }).then((response) => {
        expect(response.status).to.eq(200);
        expect(response.body.data.boards).to.be.an("array");
        expect(response.body.data.boards).to.have.length(0);
      });
    });
  });
});
