describe("UI Authentication Flow", () => {
  let testUser: any;

  beforeEach(() => {
    cy.fixture("users").then((users) => {
      testUser = users.testUser;
    });

    // Clear any existing auth tokens
    cy.clearAuthToken();
  });

  describe("Auth Page", () => {
    it("should load the authentication page", () => {
      cy.visit(Cypress.env("AUTH_URL"));

      cy.contains("Sign In to Kanban").should("be.visible");
      cy.get('input[name="email"]').should("be.visible");
      cy.get('input[name="password"]').should("be.visible");
      cy.contains("Sign Up").should("be.visible"); // This is the toggle button
    });

    it("should toggle between Sign Up and Sign In forms", () => {
      cy.visit(Cypress.env("AUTH_URL"));

      // Should start with Sign In form
      cy.contains("Sign In to Kanban").should("be.visible");
      cy.get('input[name="name"]').should("not.exist");

      // Click Sign Up button to switch
      cy.contains("Sign Up").click();

      cy.contains("Sign Up to Kanban").should("be.visible");
      cy.get('input[name="name"]').should("be.visible");
    });
  });

  describe("Sign Up Flow", () => {
    it("should successfully sign up a new user", () => {
      const uniqueEmail = `ui_test_${Date.now()}@example.com`;

      cy.visit(Cypress.env("AUTH_URL"));

      // Switch to Sign Up form
      cy.contains("Sign Up").click();

      // Fill out sign up form
      cy.get('input[name="email"]').type(uniqueEmail);
      cy.get('input[name="password"]').type(testUser.password);
      cy.get('input[name="name"]').type(testUser.name);

      // Submit form
      cy.get('button[type="submit"]').click();

      // Note: In current implementation, successful signup stays on auth page
      // This could be enhanced to redirect to UI app in the future
      cy.url().should("include", Cypress.env("AUTH_URL"));

      // Cleanup the created user
      cy.cleanupTestUser(uniqueEmail);
    });

    it("should show error for duplicate email", () => {
      const duplicateEmail = `duplicate_ui_${Date.now()}@example.com`;

      // Create user via API first
      cy.signUpUser(duplicateEmail, testUser.password, testUser.name);

      // Try to sign up with same email via UI
      cy.visit(Cypress.env("AUTH_URL"));

      // Switch to Sign Up form
      cy.contains("Sign Up").click();

      cy.get('input[name="email"]').type(duplicateEmail);
      cy.get('input[name="password"]').type(testUser.password);
      cy.get('input[name="name"]').type(testUser.name);

      cy.get('button[type="submit"]').click();

      // Should not redirect (stay on auth page due to error)
      cy.url().should("include", Cypress.env("AUTH_URL"));
    });

    it("should validate required fields", () => {
      cy.visit(Cypress.env("AUTH_URL"));

      // Try to submit without filling fields
      cy.get('button[type="submit"]').click();

      // Should show validation errors or prevent submission (checking HTML5 validation)
      cy.get('input[name="email"]:invalid').should("exist");
    });
  });

  describe("Sign In Flow", () => {
    it("should successfully sign in existing user", () => {
      cy.visit(Cypress.env("AUTH_URL"));

      // Switch to Sign In form
      cy.contains("Sign In").click();

      // Fill out sign in form using seeded user
      cy.get('input[name="email"]').type(Cypress.env("CYPRESS_USER"));
      cy.get('input[name="password"]').type(Cypress.env("CYPRESS_PASSWORD"));

      // Submit form
      cy.get('button[type="submit"]').click();

      // Note: In current implementation, successful signin stays on auth page
      // This could be enhanced to redirect to UI app in the future
      cy.url().should("include", Cypress.env("AUTH_URL"));
    });

    it("should show error for invalid credentials", () => {
      cy.visit(Cypress.env("AUTH_URL"));

      // Switch to Sign In form
      cy.contains("Sign In").click();

      // Use invalid credentials
      cy.get('input[name="email"]').type("<EMAIL>");
      cy.get('input[name="password"]').type("wrongpassword");

      cy.get('button[type="submit"]').click();

      // Should not redirect (stay on auth page due to error)
      cy.url().should("include", Cypress.env("AUTH_URL"));
    });
  });

  describe("Google OAuth Flow", () => {
    it("should display Google sign in option", () => {
      cy.visit(Cypress.env("AUTH_URL"));

      // Should have Google sign in button
      cy.contains("Sign in with Google").should("be.visible");
    });

    // Note: Testing actual Google OAuth flow would require additional setup
    // with test Google accounts or mocking the OAuth provider
    it("should handle Google OAuth button click", () => {
      cy.visit(Cypress.env("AUTH_URL"));

      // Click Google sign in button (this would typically redirect to Google)
      cy.contains("Sign in with Google").click();

      // In a real test, this would redirect to Google OAuth
      // For now, we just verify the button is clickable
    });
  });
});
