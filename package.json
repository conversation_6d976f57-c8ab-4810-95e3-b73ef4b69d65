{"name": "kanban-mono-repo", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "dev:ui": "turbo run dev --filter=@kanban/ui", "dev:ui:watch": " turbo run build && turbo run dev --filter=@kanban/design-system --filter=@kanban/ui", "build:api": "turbo run build --filter=@kanban/api", "dev:api": "turbo run dev --filter=@kanban/api", "dev:auth": "turbo run dev --filter=@kanban/auth", "codegen:server": "pnpm --filter @kanban/graphql-schema run codegen:server", "codegen:client": "pnpm --filter @kanban/graphql-schema run codegen:client", "lint": "turbo run lint", "integrations": "dotnet run --project tools/Kanban.Integrations", "clean": "turbo run clean", "test": "turbo run test", "test:e2e": "turbo run test:e2e --filter=@kanban/e2e", "test:e2e:open": "turbo run cy:open --filter=@kanban/e2e", "test:e2e:api": "turbo run cy:run:api --filter=@kanban/e2e", "test:e2e:ui": "turbo run cy:run:ui --filter=@kanban/e2e", "install:all": "pnpm install"}, "dependencies": {"@apollo/client": "^3.13.9", "graphql": "^16.11.0", "next": "^15.4.5", "react": "^19.1.1", "react-dom": "^19.1.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^24.1.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@types/uuid": "^10.0.0", "eslint": "^9.32.0", "eslint-config-next": "^15.4.5", "turbo": "^2.5.5", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.13.1"}